/**
 * Get today's date range in Turkey timezone for database queries
 * Returns UTC timestamps for start and end of today in Turkey
 */
export function getTurkeyDateRange(): { todayStr: string; tomorrowStr: string } {
  const now = new Date();
  
  // Get Turkey date components using Intl API
  const turkeyFormatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'Europe/Istanbul',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
  
  const turkeyDateStr = turkeyFormatter.format(now); // "2025-07-23"
  const [year, month, day] = turkeyDateStr.split('-').map(Number);
  
  // Create midnight in Turkey timezone, then convert to UTC
  // Turkey midnight = UTC time - 3 hours
  const turkeyMidnight = new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
  const todayStr = new Date(turkeyMidnight.getTime() - (3 * 60 * 60 * 1000)).toISOString();
  
  const tomorrowMidnight = new Date(Date.UTC(year, month - 1, day + 1, 0, 0, 0, 0));
  const tomorrowStr = new Date(tomorrowMidnight.getTime() - (3 * 60 * 60 * 1000)).toISOString();
  
  return { todayStr, tomorrowStr };
}

/**
 * Get the current date and time in Turkey timezone
 */
export function getTurkeyTime(): Date {
  const now = new Date();
  // Use the proper way to get Turkey time using Intl API
  const turkeyTimeString = now.toLocaleString("en-CA", { 
    timeZone: "Europe/Istanbul",
    year: "numeric",
    month: "2-digit", 
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false
  });
  
  // Parse the string back to a Date object
  // Format will be: "2025-07-23, 22:20:15"
  const [datePart, timePart] = turkeyTimeString.split(", ");
  const [year, month, day] = datePart.split("-");
  const [hour, minute, second] = timePart.split(":");
  
  return new Date(
    parseInt(year),
    parseInt(month) - 1, // Month is 0-based
    parseInt(day),
    parseInt(hour),
    parseInt(minute),
    parseInt(second)
  );
}

/**
 * Format a date for display in Turkey timezone
 */
export function formatDateTurkey(date: Date, options?: Intl.DateTimeFormatOptions): string {
  return date.toLocaleString("tr-TR", {
    timeZone: "Europe/Istanbul",
    ...options
  });
}

/**
 * Check if a given date is today in Turkey timezone
 */
export function isTodayInTurkey(date: Date): boolean {
  const { todayStr, tomorrowStr } = getTurkeyDateRange();
  const todayStart = new Date(todayStr);
  const todayEnd = new Date(tomorrowStr);

  return date >= todayStart && date < todayEnd;
}

/**
 * Creates a proper UTC timestamp from Turkish local time
 * This fixes the issue where Turkish local time was being treated as UTC
 *
 * @returns ISO string representing the correct UTC time
 */
export function createUTCTimestamp(): string {
  const localNow = new Date();

  // Turkey is UTC+3 (3 hours ahead of UTC)
  const turkishOffsetMs = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

  // Convert Turkish local time to proper UTC by subtracting the offset
  const properUTC = new Date(localNow.getTime() - turkishOffsetMs);

  console.log('🕐 Timezone conversion:', {
    turkishLocalTime: localNow.toString(),
    turkishAsISO: localNow.toISOString(),
    properUTC: properUTC.toISOString(),
    note: 'Storing proper UTC time (Turkish time - 3 hours)'
  });

  return properUTC.toISOString();
}

/**
 * Creates a proper UTC Date object from Turkish local time
 *
 * @returns Date object representing the correct UTC time
 */
export function createUTCDate(): Date {
  const localNow = new Date();
  const turkishOffsetMs = 3 * 60 * 60 * 1000; // 3 hours in milliseconds
  return new Date(localNow.getTime() - turkishOffsetMs);
}
