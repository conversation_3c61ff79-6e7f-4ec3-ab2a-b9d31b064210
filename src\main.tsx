import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import * as serviceWorkerRegistration from "./utils/serviceWorkerRegistration";

// Suppress development-only console messages for cleaner production console
const originalConsoleLog = console.log;
const originalConsoleInfo = console.info;
const originalConsoleWarn = console.warn;

// Helper function to check if message should be filtered
const isDevelopmentMessage = (message: string) => {
  return message.includes('React DevTools') ||
    message.includes('Download the React DevTools') ||
    message.includes('Service Worker') ||
    message.includes('Caching static assets') ||
    message.includes('Static assets cached') ||
    message.includes('Banner not shown') ||
    message.includes('beforeinstallprompt') ||
    message.includes('Fetching settings for room:') ||
    message.includes('Teacher permissions:') ||
    message.includes('Settings found:') ||
    message.includes('✅ Subscription created:') ||
    message.includes('🧹 Subscription cleaned up:') ||
    message.includes('Image URL being set:') ||
    message.includes('Getting current location...') ||
    message.includes('Current position:') ||
    message.includes('Verifying table and permissions...') ||
    message.includes('Table exists and teacher has access') ||
    message.includes('Table exists and admin has access') ||
    message.includes('Saving location data:') ||
    message.includes('Location saved successfully:') ||
    message.includes('Saving radius:') ||
    message.includes('Update result:') ||
    message.includes('Attempting to save room location:') ||
    message.includes('Verifying saved location...') ||
    message.includes('Saving settings:') ||
    message.includes('Saving block settings for block:') ||
    message.includes('Saving room settings for room:') ||
    message.includes('No admin profiles found to migrate') ||
    message.includes('Admin dashboard tab is now visible') ||
    message.includes('AdminStudentDirectory:') ||
    message.includes('No students in state, fetching') ||
    message.includes('Fetching students...') ||
    message.includes('Raw student profiles:') ||
    message.includes('Processing student:') ||
    message.includes('Processed student profiles:') ||
    message.includes('Invitation code state changed to:') ||
    message.includes('Current school data in useEffect:') ||
    message.includes('Invitation code from currentSchool:') ||
    message.includes('Setting invitation code from school data:') ||
    message.includes('Branding data query result:') ||
    message.includes('Loaded branding data from school_branding table:') ||
    message.includes('Location verification settings table exists') ||
    message.includes('Fetched blocks for QR Generator:') ||
    message.includes('Fetched rooms for QR Generator:') ||
    message.includes('🚀 Initializing Automatic QR Service') ||
    message.includes('🤖 Initializing Automatic QR Generation') ||
    message.includes('🔍 Checking attendance time ranges') ||
    message.includes('📋 Found') ||
    message.includes('✅ Automatic QR Service initialized') ||
    message.includes('Email config loaded from database') ||
    message.includes('SMS config loaded from database') ||
    message.includes('Email config loaded from localStorage') ||
    message.includes('SMS config loaded from localStorage') ||
    message.includes('🔔 Automated Reminder Service started') ||
    message.includes('🔔 Automated Reminder Service stopped') ||
    message.includes('🧹 Cleaning up') ||
    message.includes('🧹 Cleaned up subscription:') ||
    message.includes('✅ All subscriptions cleaned up') ||
    message.includes('Created localized notification') ||
    message.includes('Bulk notification creation:') ||
    message.includes('Deleting block') ||
    message.includes('Starting deletion of room:') ||
    message.includes('Successfully deleted room:') ||
    message.includes('Deleting block-level references') ||
    message.includes('Updated profiles to remove block assignment') ||
    message.includes('Deleted location verification settings') ||
    message.includes('Deleted attendance records') ||
    message.includes('Deleted QR sessions') ||
    message.includes('📊 Found') ||
    message.includes('📊 Created') ||
    message.includes('📊 Fetching') ||
    message.includes('🎯 ScanFeedback') ||
    message.includes('🎯 Setting display scans') ||
    message.includes('🔄 recentScans state changed') ||
    message.includes('🔍 Fetching latest QR code') ||
    message.includes('📅 Found QR with expiry') ||
    message.includes('⏱️ Time until new expiry') ||
    message.includes('⏰ Fetched QR code has already expired') ||
    message.includes('attendance records for today') ||
    message.includes('recent scans') ||
    message.includes('scan events') ||
    message.includes('Running database migrations') ||
    message.includes('Checking schools table columns') ||
    message.includes('Schools table columns updated successfully') ||
    message.includes('Creating system_school_settings_overrides table') ||
    message.includes('System school settings overrides table created successfully') ||
    message.includes('Running system admin code migration') ||
    message.includes('System_settings table exists') ||
    message.includes('SchoolThemeProvider: School changed, loading branding...') ||
    message.includes('SchoolThemeProvider: Loading branding for school:') ||
    message.includes('SchoolThemeProvider: Branding query result:') ||
    message.includes('SchoolThemeProvider: Found branding colors:') ||
    message.includes('SchoolThemeProvider: Applying branding colors to document') ||
    message.includes('SchoolThemeProvider: Applying theme to document:') ||
    message.includes('SchoolThemeProvider: CSS variables applied successfully') ||
    message.includes('SchoolThemeProvider: Injected branding CSS:') ||
    message.includes('AdminSettings: Fetching blocks and rooms for school:') ||
    message.includes('AdminSettings: Found blocks:') ||
    message.includes('AdminSettings: Found rooms:') ||
    message.includes('Previewing primary color:') ||
    message.includes('Previewing secondary color:') ||
    message.includes('Teacher: Fetching blocks and rooms for school:') ||
    message.includes('Teacher: Filtering rooms. Selected block:') ||
    message.includes('Teacher: Showing all rooms:') ||
    message.includes('Teacher: Found rooms:') ||
    message.includes('Teacher: Found blocks:') ||
    message.includes('🛑 Automatic excuse cleanup stopped') ||
    message.includes('🛑 Stopping Automatic QR Service...') ||
    message.includes('✅ Automatic QR Service stopped') ||
    message.includes('Teacher: Filtered rooms for block') ||
    message.includes('WebSocket connection to') ||
    message.includes('WebSocket is closed before the connection is established') ||
    message.includes('Error during service worker registration:') ||
    message.includes('This web app is being served cache-first by a service') ||
    message.includes('New content is available and will be used when all') ||
    message.includes('Content is cached for offline use.') ||
    message.includes('System admin code upserted successfully') ||
    message.includes('Running database cleanup settings migration') ||
    message.includes('Database cleanup settings table already exists') ||
    message.includes('Running table exists function migration') ||
    message.includes('Table exists function migration completed successfully') ||
    message.includes('Running feedback system migration') ||
    message.includes('Feedback system migration completed successfully') ||
    message.includes('Database migrations completed') ||
    message.includes('Fetching users from database') ||
    message.includes('Raw data from database:') ||
    message.includes('User ') && message.includes('profile_completed=') ||
    message.includes('Transformed users:') ||
    message.includes('Successfully retrieved system admin code from database') ||
    message.includes('Running database cleanup settings migration (direct SQL)') ||
    message.includes('Error fetching footer settings:') ||
    message.includes('Error details:') ||
    message.includes('No footer settings found, returning default settings') ||
    message.includes('Could not find element') ||
    message.includes('DataStore.get: namespace is required') ||
    message.includes('ErrorUtils caught an error') ||
    message.includes('DOMSubtreeModified') ||
    message.includes('Facebook XFBML parse error') ||
    message.includes('Failed to load Facebook SDK') ||
    message.includes('X-Frame-Options') ||
    message.includes('Refused to display') ||
    message.includes('🎭 GreetingNotification render:') ||
    message.includes('🤖 AIAssistant greeting state:') ||
    message.includes('🔍 Step') ||
    message.includes('🔄 AI') ||
    message.includes('⏰ AI') ||
    message.includes('Setting up PWA install') ||
    message.includes('App not detected as installed') ||
    message.includes('PWA beforeinstallprompt') ||
    message.includes('Deferred prompt stored') ||
    message.includes('WebSocket connection to') ||
    message.includes('WebSocket is closed before') ||
    message.includes('Using stored language preference:') ||
    message.includes('Language initialized to:') ||
    message.includes('Using language from profile:') ||
    message.includes('Found language in profile:') ||
    message.includes('Detected language:') ||
    message.includes('Resources loaded for') ||
    message.includes('Resources reloaded for') ||
    message.includes('Current resources:') ||
    message.includes('Changing language to:') ||
    message.includes('Setting language to Turkish based on browser detection') ||
    message.includes('Setting language to English (default or browser detection)') ||
    message.includes('LanguageUpdater: Language changed to');
};

console.log = (...args) => {
  const message = args.join(' ');
  if (!isDevelopmentMessage(message)) {
    originalConsoleLog.apply(console, args);
  }
};

console.info = (...args) => {
  const message = args.join(' ');
  if (!isDevelopmentMessage(message)) {
    originalConsoleInfo.apply(console, args);
  }
};

console.warn = (...args) => {
  const message = args.join(' ');
  if (!isDevelopmentMessage(message)) {
    originalConsoleWarn.apply(console, args);
  }
};

createRoot(document.getElementById("root")!).render(<App />);

// Service Worker Registration
// Currently disabled to prevent 404 errors in production
// To enable offline functionality, uncomment the register() call below
// and ensure service-worker.js is properly built and deployed

// serviceWorkerRegistration.register({
//   onUpdate: (registration) => {
//     // When new content is available, show a notification
//     if (registration && registration.waiting) {
//       // Show a toast notification
//       if (window.confirm("New version available! Update now?")) {
//         registration.waiting.postMessage({ type: "SKIP_WAITING" });
//         window.location.reload();
//       }
//     }
//   },
// });

// For now, unregister any existing service workers to prevent issues
serviceWorkerRegistration.unregister();
