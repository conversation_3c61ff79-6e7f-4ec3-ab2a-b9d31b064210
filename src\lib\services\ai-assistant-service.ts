/**
 * AI Assistant Service - The brain of our intelligent chatbot
 * Handles DeepSeek R1 integration via OpenRouter, context management, and intelligent responses
 */

export interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  actions?: QuickAction[];
  isTyping?: boolean;
}

export interface QuickAction {
  id: string;
  label: string;
  icon?: string;
  action: () => void;
  message?: string;
}

export interface UserContext {
  userId: string;
  userName: string;
  userRole: 'student' | 'teacher' | 'admin';
  schoolId: string;
  schoolName: string;
  currentPage?: string;
  attendanceData?: any;
  profileData?: any;
}

export interface BotCapabilities {
  canScanQR: boolean;
  canCheckAttendance: boolean;
  canAccessProfile: boolean;
  canSendNotifications: boolean;
  canAccessSchedule: boolean;
}

class AIAssistantService {
  private openRouterApiKey: string = '';
  private conversationHistory: ChatMessage[] = [];
  private userContext: UserContext | null = null;
  private isOnline: boolean = navigator.onLine;
  private knowledgeBase: Map<string, string> = new Map();
  private systemPrompt: string = '';
  private contextualMemory: Map<string, any> = new Map();
  private apiBaseUrl: string = 'https://openrouter.ai/api/v1';
  //'';
  //';
  private modelName: string = 'deepseek/deepseek-r1';
  private currentLanguage: string = 'en';
  private translateFunction: ((key: string) => string) | null = null;

  constructor() {
    this.initializeIntelligentSystem();
    this.setupOnlineDetection();
    this.initializeApiKey();
  }

  /**
   * Initialize OpenRouter API key
   */
  private initializeApiKey(): void {
    // Get API key from environment variables
    this.openRouterApiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';

    if (!this.openRouterApiKey) {
      console.warn('OpenRouter API key not found. AI Assistant will use fallback responses.');
    }
  }

  /**
   * Initialize intelligent AI system with advanced contextual understanding
   */
  private initializeIntelligentSystem(): void {
    this.systemPrompt = `You are ATS Assistant, an intelligent AI helper for the Attendance Tracking System (ATS). You are knowledgeable, friendly, and contextually aware.

CONTEXT: You are helping users with a comprehensive school attendance tracking system that includes:
- QR code scanning for attendance
- Biometric authentication (fingerprint/face recognition)
- Location-based verification
- Real-time notifications and alerts
- Attendance reports and analytics
- Multi-language support (English/Turkish)
- PWA capabilities with offline support

USER ROLES:
- Students: Mark attendance, view records, receive notifications
- Teachers: Take attendance, Export attendance datas, monitor rooms, send alerts
- Admins: Manage system settings, users, reports, school configuration, generate QR codes, manage QR codes

KEY CAPABILITIES:
- Provide step-by-step guidance for all features
- Troubleshoot technical issues
- Explain system functionality clearly
- Offer contextual suggestions based on user role
- Help with setup and configuration
- Answer questions about attendance policies

RESPONSE STYLE:
- Be helpful, friendly, and professional
- Use emojis appropriately to make responses engaging
- Provide clear, actionable instructions
- Keep responses concise but comprehensive
- Adapt language based on user's technical level
- Always prioritize user success and understanding

Remember: You have access to real-time context about the user's role, school, and current page. Use this information to provide highly relevant and personalized assistance.`;

    this.initializeKnowledgeBase();
    this.initializeContextualPatterns();
  }

  /**
   * Initialize comprehensive knowledge base for intelligent responses
   */
  private initializeKnowledgeBase(): void {
    // Comprehensive System Overview
    this.knowledgeBase.set('system_overview', 'Our Attendance Tracking System (ATS) is a comprehensive, modern solution that helps schools efficiently monitor student attendance using multiple verification methods including QR codes, biometric authentication, and location-based tracking.');

    // Verification Methods - Detailed
    this.knowledgeBase.set('qr_scanning', 'QR Code Scanning: Admins generate unique QR codes for every room/blocks. Students use their mobile devices to scan these codes for quick attendance marking. Steps: 1) Admin displays QR code 2) Student opens app and taps "Scan QR" 3) Point camera at code 4) Attendance automatically recorded after verifying their identity and location');
    this.knowledgeBase.set('biometric_verification', 'Biometric Authentication: Uses fingerprint or facial recognition for secure attendance marking, preventing proxy attendance. Setup: 1) Go to Profile Settings 2) Enable "Biometric Authentication" 3) Register your fingerprint/face 4) Use biometric methods during the check in');
    this.knowledgeBase.set('location_tracking', 'Location-Based Attendance: Ensures students are physically present within school premises using GPS verification. The system checks if you are within the designated school boundaries before allowing attendance marking.');

    // User Roles and Capabilities
    this.knowledgeBase.set('student_capabilities', 'As a Student you can: Mark attendance using your preferred verification method, view your attendance history and statistics, receive real-time notifications about your attendance status, access your class schedule, and track your attendance percentage.');
    this.knowledgeBase.set('teacher_capabilities', 'As a Teacher you can: Take attendance for your classes, generate and display QR codes, monitor real-time student attendance, send absence notifications to parents, view detailed class attendance reports, and manage student attendance records.');
    this.knowledgeBase.set('admin_capabilities', 'As an Administrator you can: Manage all users and their permissions, configure system settings and attendance policies, access comprehensive analytics and reports, oversee multiple schools (if applicable), customize system branding and notifications.');

    // Step-by-Step Guides
    this.knowledgeBase.set('qr_scan_guide', 'How to scan QR code: 1) Open the ATS app 2) Tap "Scan QR" button on your dashboard 3) Allow camera access when prompted 4) Point your camera at the QR code displayed by your teacher 5) Wait for automatic detection and confirmation');
    this.knowledgeBase.set('attendance_check_guide', 'How to check your attendance: 1) Open the app and go to "Attendance" tab 2) View your daily attendance records 3) Check monthly/weekly summaries 4) See your overall attendance percentage 5) Review any absence notifications');
    this.knowledgeBase.set('profile_update_guide', 'How to update your profile: 1) Tap "Profile" in the bottom navigation 2) Tap "Edit Profile" button 3) Update your personal information 4) Upload a new profile photo if needed 5) Save your changes');

    // Advanced Features
    this.knowledgeBase.set('smart_features', 'Smart Features: Real-time attendance monitoring, automated absence notifications to parents, attendance analytics and insights, customizable attendance policies per school, multi-language support (English/Turkish), offline functionality with sync, and push notifications.');
    this.knowledgeBase.set('reporting_system', 'Reporting System: Generate detailed reports by student, class, date range, or custom criteria. Export in PDF, Excel, or CSV formats. View attendance trends, identify patterns, and get insights for better attendance management.');
    this.knowledgeBase.set('notification_system', 'Notification System: Receive real-time push notifications for attendance alerts, absence warnings, system updates, and important announcements. Customize notification preferences in Settings.');

    // Technical Capabilities
    this.knowledgeBase.set('pwa_technology', 'Progressive Web App (PWA): Install our app on any device like a native app. Works offline with automatic data synchronization when online. Receive push notifications and enjoy an app-like experience across all platforms.');
    this.knowledgeBase.set('offline_functionality', 'Offline Capability: Continue using the app without internet connection. View cached attendance data, access profile information, browse app features. Data automatically syncs when connection is restored.');

    // Troubleshooting
    this.knowledgeBase.set('camera_issues', 'Camera Problems: If QR scanning is not working: 1) Check browser permissions and allow camera access 2) Refresh the page 3) Clear browser cache 4) Try using a different browser 5) Ensure good lighting for QR code scanning');
    this.knowledgeBase.set('biometric_issues', 'Biometric Problems: If authentication fails: 1) Ensure you use only fingerprint/face recognition 2) Avoid using PIN/pattern/password 3) Re-register your biometric data 4) Clean your device sensor 5) Try in good lighting conditions');
    this.knowledgeBase.set('location_issues', 'Location Problems: If location verification fails: 1) Enable location services in your browser 2) Allow location access for the app 3) Ensure you are within school premises 4) Check GPS signal strength 5) Try refreshing your location');

    // School Management
    this.knowledgeBase.set('school_management', 'Multi-School Support: Each school has completely isolated data and settings. Administrators can customize branding, attendance policies, and user permissions specific to their school. Students and teachers only see data relevant to their school.');
  }

  /**
   * Initialize contextual patterns for intelligent responses
   */
  private initializeContextualPatterns(): void {
    // Intent recognition patterns
    this.contextualMemory.set('greeting_patterns', ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening', 'greetings']);
    this.contextualMemory.set('help_patterns', ['help', 'how to', 'can you', 'guide me', 'show me', 'explain', 'teach me', 'assist']);
    this.contextualMemory.set('problem_patterns', ['problem', 'issue', 'error', 'not working', 'broken', 'bug', 'trouble', 'difficulty']);
    this.contextualMemory.set('feature_patterns', ['what is', 'what does', 'how does', 'tell me about', 'explain', 'describe']);
    this.contextualMemory.set('gratitude_patterns', ['thank you', 'thanks', 'appreciate', 'grateful', 'helpful']);

    // Common topics and their related keywords
    this.contextualMemory.set('attendance_keywords', ['attendance', 'present', 'absent', 'check in', 'mark', 'record']);
    this.contextualMemory.set('qr_keywords', ['qr', 'qr code', 'scan', 'camera', 'code']);
    this.contextualMemory.set('biometric_keywords', ['biometric', 'fingerprint', 'face', 'facial', 'recognition', 'authentication']);
    this.contextualMemory.set('location_keywords', ['location', 'gps', 'premises', 'school grounds', 'position']);
    this.contextualMemory.set('notification_keywords', ['notification', 'alert', 'push', 'message', 'reminder']);
  }

  /**
   * Setup online/offline detection
   */
  private setupOnlineDetection(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * Set user context for personalized responses
   */
  setUserContext(context: UserContext): void {
    this.userContext = context;
  }

  /**
   * Set current language for internationalized responses
   */
  setLanguage(language: string): void {
    this.currentLanguage = language;
    this.updateSystemPromptForLanguage();
  }

  /**
   * Set translation function for action labels
   */
  setTranslateFunction(translateFn: (key: string) => string): void {
    this.translateFunction = translateFn;
  }

  /**
   * Get translated action label
   */
  private getActionLabel(key: string, fallback: string): string {
    if (this.translateFunction) {
      return this.translateFunction(`aiAssistant.actions.${key}`);
    }
    return fallback;
  }

  /**
   * Get translated action message
   */
  private getActionMessage(key: string, fallback: string): string {
    if (this.translateFunction) {
      return this.translateFunction(`aiAssistant.actionMessages.${key}`);
    }
    return fallback;
  }

  /**
   * Get translated fallback response
   */
  private getFallbackResponse(key: string, fallback: string): string {
    if (this.translateFunction) {
      return this.translateFunction(`aiAssistant.fallbackResponses.${key}`);
    }
    return fallback;
  }

  /**
   * Update system prompt based on current language
   */
  private updateSystemPromptForLanguage(): void {
    if (this.currentLanguage === 'tr') {
      this.systemPrompt = `Sen ATS Asistanısın, Devam Takip Sistemi (ATS) için akıllı bir AI yardımcısısın. Bilgili, arkadaş canlısı ve bağlamsal olarak farkındasın.

ÖNEMLİ BAĞLAM: Bu, okul/kampüs yurtları için bir YURT DEVAM TAKİP SİSTEMİ'dir, sınıf devamı DEĞİL. Öğrenciler yurt odalarına giriş yaparlar, sınıflara değil.

SİSTEM GENEL BAKIŞ:
- Öğrenciler bloklar/binalar içindeki yurt odalarında yaşarlar
- Öğrenciler atanmış yurt odalarına günlük giriş yapmalıdır
- QR kodları SADECE OKUL YÖNETİCİLERİ tarafından oluşturulur (öğretmenler tarafından değil)
- QR kodları her yurt odasındaki tabletlerde görüntülenir
- Öğrenciler yurt devamını işaretlemek için QR kodlarını tarar

KULLANICI ROLLERİ VE YETKİLER:
- ÖĞRENCİLER: QR kod tarama, devam kontrol etme, profil görüntüleme
- ÖĞRETMENLER: Öğrenci devam raporlarını görüntüleme, bildirimler alma
- YÖNETİCİLER: QR kod oluşturma, tüm raporlar, kullanıcı yönetimi, sistem ayarları

Her zaman Türkçe yanıt ver ve kullanıcının rolüne uygun bilgi sağla.`;
    } else {
      // Keep the existing English system prompt
      this.systemPrompt = `You are ATS Assistant, an intelligent AI helper for the Attendance Tracking System (ATS). You are knowledgeable, friendly, and contextually aware.

IMPORTANT CONTEXT: This is a DORMITORY ATTENDANCE TRACKING SYSTEM for school/campus dormitories, NOT classroom attendance. Students check in to their dormitory rooms, not classes.

SYSTEM OVERVIEW:
- Students live in dormitory rooms within blocks/buildings
- Students must check in to their assigned dormitory rooms daily
- QR codes are generated by SCHOOL ADMINS ONLY (not teachers)
- QR codes are displayed on tablets in each dormitory room
- Students scan QR codes to mark their dormitory attendance

USER ROLES AND CAPABILITIES:
- STUDENTS: QR code scanning, attendance checking, profile viewing
- TEACHERS: View student attendance reports, receive notifications
- ADMINS: QR code generation, all reports, user management, system settings

Always respond in English and provide role-appropriate information.`;
    }
  }

  /**
   * Get conversation history
   */
  getConversationHistory(): ChatMessage[] {
    return this.conversationHistory;
  }

  /**
   * Clear conversation history
   */
  clearConversation(): void {
    this.conversationHistory = [];
  }

  /**
   * Send a message to the AI assistant
   */
  async sendMessage(userMessage: string): Promise<ChatMessage> {
    // Add user message to history
    const userMsg: ChatMessage = {
      id: this.generateId(),
      type: 'user',
      content: userMessage,
      timestamp: new Date()
    };
    this.conversationHistory.push(userMsg);

    // Generate bot response
    const botResponse = await this.generateResponse(userMessage);
    
    // Add bot message to history
    const botMsg: ChatMessage = {
      id: this.generateId(),
      type: 'bot',
      content: botResponse.content,
      timestamp: new Date(),
      actions: botResponse.actions
    };
    this.conversationHistory.push(botMsg);

    return botMsg;
  }

  /**
   * Generate intelligent response using DeepSeek R1 via OpenRouter
   */
  private async generateResponse(userMessage: string): Promise<{content: string, actions?: QuickAction[]}> {
    // Try AI API first, fallback to pattern matching if needed
    if (this.openRouterApiKey && this.isOnline) {
      try {
        return await this.generateAIResponse(userMessage);
      } catch (error) {
        console.error('AI API failed, falling back to pattern matching:', error);
        return this.generateFallbackResponse(userMessage);
      }
    } else {
      // Use fallback pattern matching when offline or no API key
      return this.generateFallbackResponse(userMessage);
    }
  }

  /**
   * Generate response using DeepSeek R1 API
   */
  private async generateAIResponse(userMessage: string): Promise<{content: string, actions?: QuickAction[]}> {
    const contextualPrompt = this.buildContextualPrompt(userMessage);

    const response = await fetch(`${this.apiBaseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'ATS Assistant'
      },
      body: JSON.stringify({
        model: this.modelName,
        messages: [
          {
            role: 'system',
            content: this.systemPrompt
          },
          {
            role: 'user',
            content: contextualPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiContent = data.choices?.[0]?.message?.content || 'I apologize, but I encountered an issue processing your request. Please try again.';

    // Generate contextual quick actions based on the response
    const actions = this.generateContextualActions(userMessage, aiContent);

    return {
      content: aiContent,
      actions
    };
  }

  /**
   * Build contextual prompt with user context and conversation history
   */
  private buildContextualPrompt(userMessage: string): string {
    let prompt = `USER MESSAGE: "${userMessage}"\n\n`;

    // Add user context if available
    if (this.userContext) {
      prompt += `USER CONTEXT:
- Role: ${this.userContext.userRole}
- Name: ${this.userContext.userName}
- School: ${this.userContext.schoolName}
- Current Page: ${this.userContext.currentPage || 'Unknown'}

`;
    }

    // Add recent conversation history for context
    const recentHistory = this.conversationHistory.slice(-6); // Last 3 exchanges
    if (recentHistory.length > 0) {
      prompt += `RECENT CONVERSATION:\n`;
      recentHistory.forEach(msg => {
        prompt += `${msg.type.toUpperCase()}: ${msg.content}\n`;
      });
      prompt += '\n';
    }

    prompt += `Please provide a helpful, contextual response as ATS Assistant. If the user needs step-by-step guidance, provide clear instructions. If they're reporting an issue, offer specific troubleshooting steps.`;

    return prompt;
  }

  /**
   * Generate contextual quick actions based on user message and AI response
   */
  private generateContextualActions(userMessage: string, aiResponse: string): QuickAction[] {
    const actions: QuickAction[] = [];
    const lowerMessage = userMessage.toLowerCase();
    const lowerResponse = aiResponse.toLowerCase();

    // QR-related actions
    if (lowerMessage.includes('qr') || lowerMessage.includes('scan') || lowerResponse.includes('qr')) {
      actions.push({
        id: 'qr_help',
        label: this.getActionLabel('qrScanningGuide', '📱 QR Scanning Guide'),
        action: () => {},
        message: this.getActionMessage('qrScanningInstructions', 'Show me step-by-step QR scanning instructions')
      });
    }

    // Attendance-related actions
    if (lowerMessage.includes('attendance') || lowerMessage.includes('present') || lowerResponse.includes('attendance')) {
      actions.push({
        id: 'attendance_check',
        label: '📊 Check Attendance',
        action: () => {},
        message: 'How do I check my attendance records?'
      });
    }

    // Biometric-related actions
    if (lowerMessage.includes('biometric') || lowerMessage.includes('fingerprint') || lowerMessage.includes('face')) {
      actions.push({
        id: 'biometric_setup',
        label: '🔐 Biometric Setup',
        action: () => {},
        message: 'Help me set up biometric authentication'
      });
    }

    // Problem/troubleshooting actions
    if (lowerMessage.includes('problem') || lowerMessage.includes('issue') || lowerMessage.includes('error') || lowerMessage.includes('not working')) {
      actions.push({
        id: 'troubleshoot',
        label: '🔧 More Troubleshooting',
        action: () => {},
        message: 'I need more help with technical issues'
      });
    }

    // General help actions
    if (actions.length === 0 || lowerMessage.includes('help') || lowerMessage.includes('how')) {
      actions.push({
        id: 'features',
        label: this.getActionLabel('appFeatures', '✨ App Features'),
        action: () => {},
        message: this.getActionMessage('showAppFeatures', 'What features does this app have?')
      });
      actions.push({
        id: 'more_help',
        label: this.getActionLabel('askAnotherQuestion', '❓ Ask Another Question'),
        action: () => {},
        message: this.getActionMessage('anotherQuestion', 'I have another question')
      });
    }

    return actions.slice(0, 3); // Limit to 3 actions for clean UI
  }

  /**
   * Generate fallback response using pattern matching (when AI API is unavailable)
   */
  private generateFallbackResponse(userMessage: string): {content: string, actions?: QuickAction[]} {
    const lowerMessage = userMessage.toLowerCase();

    // Analyze user intent and context
    const intent = this.analyzeUserIntent(lowerMessage);
    const context = this.extractContext(lowerMessage);
    const sentiment = this.analyzeSentiment(lowerMessage);

    // Generate contextually appropriate response
    return this.generateIntelligentResponse(userMessage, intent, context, sentiment);
  }

  /**
   * Analyze user intent from message
   */
  private analyzeUserIntent(message: string): string {
    // Greeting detection
    if (this.matchesPatterns(message, this.contextualMemory.get('greeting_patterns'))) {
      return 'greeting';
    }

    // Help request detection
    if (this.matchesPatterns(message, this.contextualMemory.get('help_patterns'))) {
      return 'help_request';
    }

    // Problem reporting detection
    if (this.matchesPatterns(message, this.contextualMemory.get('problem_patterns'))) {
      return 'problem_report';
    }

    // Feature inquiry detection
    if (this.matchesPatterns(message, this.contextualMemory.get('feature_patterns'))) {
      return 'feature_inquiry';
    }

    // Gratitude detection
    if (this.matchesPatterns(message, this.contextualMemory.get('gratitude_patterns'))) {
      return 'gratitude';
    }

    // Topic-specific intents
    if (this.matchesPatterns(message, this.contextualMemory.get('qr_keywords'))) {
      return 'qr_related';
    }

    if (this.matchesPatterns(message, this.contextualMemory.get('attendance_keywords'))) {
      return 'attendance_related';
    }

    if (this.matchesPatterns(message, this.contextualMemory.get('biometric_keywords'))) {
      return 'biometric_related';
    }

    if (this.matchesPatterns(message, this.contextualMemory.get('location_keywords'))) {
      return 'location_related';
    }

    if (this.matchesPatterns(message, this.contextualMemory.get('notification_keywords'))) {
      return 'notification_related';
    }

    return 'general_inquiry';
  }

  /**
   * Extract context from user message
   */
  private extractContext(message: string): any {
    const context: any = {
      topics: [],
      urgency: 'normal',
      complexity: 'simple'
    };

    // Detect topics
    if (message.includes('qr') || message.includes('scan')) context.topics.push('qr_scanning');
    if (message.includes('attendance') || message.includes('present')) context.topics.push('attendance');
    if (message.includes('biometric') || message.includes('fingerprint')) context.topics.push('biometric');
    if (message.includes('location') || message.includes('gps')) context.topics.push('location');
    if (message.includes('notification') || message.includes('alert')) context.topics.push('notifications');
    if (message.includes('profile') || message.includes('settings')) context.topics.push('profile');
    if (message.includes('report') || message.includes('analytics')) context.topics.push('reporting');

    // Detect urgency
    if (message.includes('urgent') || message.includes('emergency') || message.includes('asap')) {
      context.urgency = 'high';
    } else if (message.includes('when you can') || message.includes('no rush')) {
      context.urgency = 'low';
    }

    // Detect complexity
    if (message.includes('step by step') || message.includes('detailed') || message.includes('explain everything')) {
      context.complexity = 'detailed';
    } else if (message.includes('quick') || message.includes('brief') || message.includes('summary')) {
      context.complexity = 'simple';
    }

    return context;
  }

  /**
   * Analyze sentiment of user message
   */
  private analyzeSentiment(message: string): string {
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'like', 'happy', 'satisfied'];
    const negativeWords = ['bad', 'terrible', 'hate', 'frustrated', 'angry', 'disappointed', 'broken', 'useless'];
    const neutralWords = ['okay', 'fine', 'normal', 'average'];

    const words = message.split(' ');
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word.toLowerCase())) positiveCount++;
      if (negativeWords.includes(word.toLowerCase())) negativeCount++;
      if (neutralWords.includes(word.toLowerCase())) neutralCount++;
    });

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * Generate intelligent, contextual response
   */
  private generateIntelligentResponse(userMessage: string, intent: string, context: any, sentiment: string): {content: string, actions?: QuickAction[]} {
    // Handle different intents with contextual awareness
    switch (intent) {
      case 'greeting':
        return this.handleGreeting(sentiment);

      case 'help_request':
        return this.handleHelpRequest(context);

      case 'problem_report':
        return this.handleProblemReport(userMessage, context, sentiment);

      case 'feature_inquiry':
        return this.handleFeatureInquiry(context);

      case 'gratitude':
        return this.handleGratitude();

      case 'qr_related':
        return this.handleQRRelated(userMessage, context);

      case 'attendance_related':
        return this.handleAttendanceRelated(userMessage, context);

      case 'biometric_related':
        return this.handleBiometricRelated(userMessage, context);

      case 'location_related':
        return this.handleLocationRelated(userMessage, context);

      case 'notification_related':
        return this.handleNotificationRelated(userMessage, context);

      default:
        return this.handleGeneralInquiry(userMessage, context);
    }
  }

  /**
   * Handle greeting with appropriate response
   */
  private handleGreeting(sentiment: string): {content: string, actions?: QuickAction[]} {
    const greetings = this.currentLanguage === 'tr' ? [
      "Merhaba! 👋 Ben ATS Asistanınızım, devam sisteminizle ilgili her konuda size yardımcı olmaya hazırım!",
      "Selam! 😊 ATS Asistanına hoş geldiniz. Bugün size nasıl yardımcı olabilirim?",
      "Selamlar! 🌟 Tüm devam takip ihtiyaçlarınızda size yardımcı olmak için buradayım."
    ] : [
      "Hello! 👋 I'm your ATS Assistant, ready to help you with anything related to our attendance system!",
      "Hi there! 😊 Welcome to ATS Assistant. How can I help you today?",
      "Greetings! 🌟 I'm here to assist you with all your attendance tracking needs!"
    ];

    const greeting = greetings[Math.floor(Math.random() * greetings.length)];

    const actions: QuickAction[] = [
      {
        id: 'qr_help',
        label: this.getActionLabel('qrScanning', this.currentLanguage === 'tr' ? '📱 QR Tarama Yardımı' : '📱 QR Scanning Help'),
        action: () => {},
        message: this.getActionMessage('qrScanningHelp', 'How do I scan QR codes?')
      },
      {
        id: 'attendance_help',
        label: this.getActionLabel('attendance', this.currentLanguage === 'tr' ? '📊 Devamı Kontrol Et' : '📊 Check Attendance'),
        action: () => {},
        message: this.getActionMessage('checkAttendance', 'How do I check my attendance?')
      },
      {
        id: 'features_help',
        label: this.getActionLabel('appFeatures', this.currentLanguage === 'tr' ? '✨ Uygulama Özellikleri' : '✨ App Features'),
        action: () => {},
        message: this.getActionMessage('showAppFeatures', 'What features does this app have?')
      }
    ];

    const helpText = this.currentLanguage === 'tr' ?
      'Size yardımcı olabileceğim bazı konular:' :
      'Here are some things I can help you with:';

    return {
      content: `${greeting}\n\n${helpText}`,
      actions
    };
  }

  /**
   * Handle help requests with contextual guidance
   */
  private handleHelpRequest(context: any): {content: string, actions?: QuickAction[]} {
    if (context.topics.length > 0) {
      // Provide specific help based on detected topics
      const topic = context.topics[0];
      const helpContent = this.getTopicSpecificHelp(topic);
      return {
        content: `I'll help you with ${topic.replace('_', ' ')}! 🤝\n\n${helpContent}`,
        actions: this.getTopicActions(topic)
      };
    }

    // General help
    const helpContent = this.getFallbackResponse('helpRequest', `I'm here to help you with our Attendance Tracking System! 🎯\n\nI can assist you with:
• QR code scanning for attendance
• Biometric authentication setup
• Checking your attendance records
• Understanding app features
• Troubleshooting issues
• Profile and settings management

What would you like help with?`);

    const actions: QuickAction[] = [
      {
        id: 'qr_help',
        label: this.getActionLabel('qrScanning', '📱 QR Scanning'),
        action: () => {},
        message: this.getActionMessage('helpWithQrScanning', 'Help me with QR scanning')
      },
      {
        id: 'biometric_help',
        label: this.getActionLabel('biometricSetup', '🔐 Biometric Setup'),
        action: () => {},
        message: this.getActionMessage('technicalProblem', 'Help me set up biometrics')
      },
      {
        id: 'attendance_help',
        label: this.getActionLabel('attendanceRecords', '📊 Attendance Records'),
        action: () => {},
        message: this.getActionMessage('helpWithAttendance', 'How do I check my attendance?')
      },
      {
        id: 'troubleshoot',
        label: this.getActionLabel('troubleshooting', '🔧 Troubleshooting'),
        action: () => {},
        message: this.getActionMessage('technicalProblem', 'I\'m having technical issues')
      }
    ];

    return { content: helpContent, actions };
  }

  /**
   * Handle problem reports with intelligent troubleshooting
   */
  private handleProblemReport(userMessage: string, context: any, sentiment: string): {content: string, actions?: QuickAction[]} {
    let response = this.getFallbackResponse('problemReport', "I understand you're experiencing an issue. Let me help you resolve it! 🔧\n\n");

    // Detect specific problems
    if (userMessage.includes('camera') || userMessage.includes('scan')) {
      response += this.knowledgeBase.get('camera_issues');
    } else if (userMessage.includes('biometric') || userMessage.includes('fingerprint')) {
      response += this.knowledgeBase.get('biometric_issues');
    } else if (userMessage.includes('location') || userMessage.includes('gps')) {
      response += this.knowledgeBase.get('location_issues');
    } else {
      response += this.getFallbackResponse('problemReport', "Could you please provide more details about the specific issue you're facing? This will help me give you the most accurate solution.");
    }

    const actions: QuickAction[] = [
      {
        id: 'camera_help',
        label: this.getActionLabel('cameraIssues', '📷 Camera Issues'),
        action: () => {},
        message: this.getActionMessage('cameraIssues', 'Camera is not working for QR scanning')
      },
      {
        id: 'biometric_help',
        label: this.getActionLabel('biometricIssues', '🔐 Biometric Issues'),
        action: () => {},
        message: this.getActionMessage('biometricIssues', 'Biometric authentication is failing')
      },
      {
        id: 'location_help',
        label: this.getActionLabel('locationIssues', '📍 Location Issues'),
        action: () => {},
        message: this.getActionMessage('locationIssues', 'Location verification is not working')
      },
      {
        id: 'general_support',
        label: this.getActionLabel('describeIssue', '💬 Describe Issue'),
        action: () => {},
        message: this.getActionMessage('describeIssue', 'Let me describe my specific problem')
      }
    ];

    return { content: response, actions };
  }

  /**
   * Handle QR-related queries
   */
  private handleQRRelated(userMessage: string, context: any): {content: string, actions?: QuickAction[]} {
    const content = this.getFallbackResponse('qrRelated', `I'll help you with QR code scanning! 📱\n\n${this.knowledgeBase.get('qr_scanning')}\n\nIs there something specific about QR scanning you'd like to know?`);

    const actions: QuickAction[] = [
      {
        id: 'qr_steps',
        label: this.getActionLabel('qrSteps', '📋 Step-by-Step Guide'),
        action: () => {},
        message: this.getActionMessage('detailedQrSteps', 'Show me detailed QR scanning steps')
      },
      {
        id: 'qr_troubleshoot',
        label: this.getActionLabel('qrProblems', '🔧 QR Problems'),
        action: () => {},
        message: this.getActionMessage('qrNotWorking', 'QR scanning is not working')
      },
      {
        id: 'camera_permission',
        label: this.getActionLabel('cameraAccess', '📷 Camera Access'),
        action: () => {},
        message: this.getActionMessage('cameraAccess', 'How do I allow camera access?')
      }
    ];

    return { content, actions };
  }

  /**
   * Handle attendance-related queries
   */
  private handleAttendanceRelated(userMessage: string, context: any): {content: string, actions?: QuickAction[]} {
    const content = this.getFallbackResponse('attendanceRelated', `I'll help you with attendance tracking! 📊\n\n${this.knowledgeBase.get('attendance_check_guide')}\n\nWhat specific aspect of attendance would you like to know about?`);

    const actions: QuickAction[] = [
      {
        id: 'check_attendance',
        label: this.getActionLabel('viewMyAttendance', '📈 View My Attendance'),
        action: () => {},
        message: this.getActionMessage('viewMyAttendance', 'How do I check my attendance records?')
      },
      {
        id: 'mark_attendance',
        label: this.getActionLabel('markAttendance', '✅ Mark Attendance'),
        action: () => {},
        message: this.getActionMessage('markAttendance', 'How do I mark my attendance?')
      },
      {
        id: 'attendance_issues',
        label: this.getActionLabel('attendanceProblems', '❓ Attendance Problems'),
        action: () => {},
        message: this.getActionMessage('attendanceIssues', 'I have issues with my attendance records')
      }
    ];

    return { content, actions };
  }

  /**
   * Handle biometric-related queries
   */
  private handleBiometricRelated(userMessage: string, context: any): {content: string, actions?: QuickAction[]} {
    const content = this.getFallbackResponse('biometricRelated', `I'll help you with biometric authentication! 🔐\n\n${this.knowledgeBase.get('biometric_verification')}\n\nWhat do you need help with regarding biometric authentication?`);

    const actions: QuickAction[] = [
      {
        id: 'setup_biometric',
        label: this.getActionLabel('setupBiometrics', '⚙️ Setup Biometrics'),
        action: () => {},
        message: this.getActionMessage('setupBiometric', 'How do I set up biometric authentication?')
      },
      {
        id: 'biometric_issues',
        label: this.getActionLabel('fixBiometricIssues', '🔧 Fix Biometric Issues'),
        action: () => {},
        message: this.getActionMessage('biometricNotWorking', 'Biometric authentication is not working')
      },
      {
        id: 'biometric_security',
        label: this.getActionLabel('securityInfo', '🛡️ Security Info'),
        action: () => {},
        message: this.getActionMessage('biometricSecurity', 'Is biometric authentication secure?')
      }
    ];

    return { content, actions };
  }

  /**
   * Utility method to match patterns
   */
  private matchesPatterns(message: string, patterns: string[]): boolean {
    if (!patterns) return false;
    return patterns.some(pattern => message.includes(pattern));
  }

  /**
   * Handle location-related queries
   */
  private handleLocationRelated(userMessage: string, context: any): {content: string, actions?: QuickAction[]} {
    const content = this.getFallbackResponse('locationRelated', `I'll help you with location-based attendance! 📍\n\n${this.knowledgeBase.get('location_tracking')}\n\nWhat do you need to know about location verification?`);

    const actions: QuickAction[] = [
      {
        id: 'location_setup',
        label: this.getActionLabel('enableLocation', '📍 Enable Location'),
        action: () => {},
        message: this.getActionMessage('enableLocation', 'How do I enable location services?')
      },
      {
        id: 'location_issues',
        label: this.getActionLabel('locationProblems', '🔧 Location Problems'),
        action: () => {},
        message: this.getActionMessage('locationNotWorking', 'Location verification is not working')
      },
      {
        id: 'location_privacy',
        label: this.getActionLabel('privacyInfo', '🔒 Privacy Info'),
        action: () => {},
        message: this.getActionMessage('locationPrivacy', 'How is my location data used?')
      }
    ];

    return { content, actions };
  }

  /**
   * Handle notification-related queries
   */
  private handleNotificationRelated(userMessage: string, context: any): {content: string, actions?: QuickAction[]} {
    const content = this.getFallbackResponse('notificationRelated', `I'll help you with notifications! 🔔\n\n${this.knowledgeBase.get('notification_system')}\n\nWhat would you like to know about notifications?`);

    const actions: QuickAction[] = [
      {
        id: 'notification_setup',
        label: this.getActionLabel('setupNotifications', '🔔 Setup Notifications'),
        action: () => {},
        message: this.getActionMessage('setupNotifications', 'How do I enable push notifications?')
      },
      {
        id: 'notification_settings',
        label: this.getActionLabel('notificationSettings', '⚙️ Notification Settings'),
        action: () => {},
        message: this.getActionMessage('notificationSettings', 'How do I customize notification preferences?')
      },
      {
        id: 'notification_issues',
        label: this.getActionLabel('notReceivingNotifications', '🔧 Not Receiving Notifications'),
        action: () => {},
        message: this.getActionMessage('notReceivingNotifications', 'I\'m not receiving notifications')
      }
    ];

    return { content, actions };
  }

  /**
   * Handle feature inquiries
   */
  private handleFeatureInquiry(context: any): {content: string, actions?: QuickAction[]} {
    const content = this.getFallbackResponse('featureInquiry', `I'd be happy to explain our app features! ✨\n\n${this.knowledgeBase.get('smart_features')}\n\nWhich feature would you like to learn more about?`);

    const actions: QuickAction[] = [
      {
        id: 'qr_features',
        label: this.getActionLabel('qrScanning', '📱 QR Scanning'),
        action: () => {},
        message: this.getActionMessage('qrCodeFeatures', 'Tell me about QR code features')
      },
      {
        id: 'biometric_features',
        label: this.getActionLabel('biometricAuth', '🔐 Biometric Auth'),
        action: () => {},
        message: this.getActionMessage('biometricAuthFeatures', 'Explain biometric authentication')
      },
      {
        id: 'reporting_features',
        label: this.getActionLabel('reportsAnalytics', '📊 Reports & Analytics'),
        action: () => {},
        message: this.getActionMessage('reportingFeatures', 'What reporting features are available?')
      },
      {
        id: 'pwa_features',
        label: this.getActionLabel('appInstallation', '📲 App Installation'),
        action: () => {},
        message: this.getActionMessage('appInstallation', 'Can I install this as an app?')
      }
    ];

    return { content, actions };
  }

  /**
   * Handle gratitude expressions
   */
  private handleGratitude(): {content: string, actions?: QuickAction[]} {
    // Get translated gratitude responses
    const responses = this.translateFunction ?
      this.translateFunction('aiAssistant.fallbackResponses.gratitudeResponses') :
      [
        "You're very welcome! 😊 I'm always here to help you with the attendance system!",
        "Happy to help! 🌟 Feel free to ask me anything else about the app!",
        "My pleasure! 🤝 Is there anything else I can assist you with today?"
      ];

    // Handle both array and string responses
    const responseArray = Array.isArray(responses) ? responses : [responses];
    const response = responseArray[Math.floor(Math.random() * responseArray.length)];

    const actions: QuickAction[] = [
      {
        id: 'more_help',
        label: this.getActionLabel('askAnotherQuestion', '❓ Ask Another Question'),
        action: () => {},
        message: this.getActionMessage('anotherQuestion', 'I have another question')
      },
      {
        id: 'explore_features',
        label: this.getActionLabel('exploreFeatures', '✨ Explore Features'),
        action: () => {},
        message: this.getActionMessage('exploreMoreFeatures', 'What else can this app do?')
      }
    ];

    return { content: response, actions };
  }

  /**
   * Handle general inquiries with intelligent responses
   */
  private handleGeneralInquiry(userMessage: string, context: any): {content: string, actions?: QuickAction[]} {
    // Try to find relevant knowledge base entries
    const relevantInfo = this.findRelevantInformation(userMessage);

    if (relevantInfo.length > 0) {
      const content = `I found some information that might help you! 💡\n\n${relevantInfo[0]}\n\nWould you like more specific information about any particular topic?`;

      const actions: QuickAction[] = [
        { id: 'more_details', label: '📖 More Details', action: () => {}, message: 'Give me more detailed information' },
        { id: 'different_topic', label: '🔄 Different Topic', action: () => {}, message: 'I need help with something else' },
        { id: 'step_by_step', label: '📋 Step-by-Step Guide', action: () => {}, message: 'Show me step-by-step instructions' }
      ];

      return { content, actions };
    }

    // Fallback response for unclear queries
    const content = this.getFallbackResponse('generalInquiry', `I'd love to help you! 🤔 Could you please be more specific about what you need assistance with?\n\nI can help you with:
• QR code scanning and attendance marking
• Biometric authentication setup
• Checking attendance records and statistics
• App features and functionality
• Troubleshooting technical issues
• Profile and settings management

What would you like to know more about?`);

    const actions: QuickAction[] = [
      {
        id: 'qr_help',
        label: this.getActionLabel('qrScanning', '📱 QR Scanning'),
        action: () => {},
        message: this.getActionMessage('helpWithQrScanning', 'Help with QR code scanning')
      },
      {
        id: 'attendance_help',
        label: this.getActionLabel('attendance', '📊 Attendance'),
        action: () => {},
        message: this.getActionMessage('helpWithAttendance', 'Help with attendance records')
      },
      {
        id: 'technical_help',
        label: this.getActionLabel('technicalIssues', '🔧 Technical Issues'),
        action: () => {},
        message: this.getActionMessage('technicalProblem', 'I have a technical problem')
      },
      {
        id: 'features_help',
        label: this.getActionLabel('appFeatures', '✨ App Features'),
        action: () => {},
        message: this.getActionMessage('showFeatures', 'Show me app features')
      }
    ];

    return { content, actions };
  }

  /**
   * Find relevant information from knowledge base
   */
  private findRelevantInformation(userMessage: string): string[] {
    const results: string[] = [];
    const lowerMessage = userMessage.toLowerCase();

    // Search through knowledge base for relevant entries
    for (const [key, value] of this.knowledgeBase.entries()) {
      if (lowerMessage.includes(key.replace('_', ' ')) ||
          value.toLowerCase().includes(lowerMessage.split(' ')[0]) ||
          this.calculateRelevanceScore(lowerMessage, value) > 0.3) {
        results.push(value);
      }
    }

    return results.slice(0, 3); // Return top 3 most relevant
  }

  /**
   * Calculate relevance score between user message and knowledge base entry
   */
  private calculateRelevanceScore(userMessage: string, knowledgeEntry: string): number {
    const userWords = userMessage.toLowerCase().split(' ');
    const entryWords = knowledgeEntry.toLowerCase().split(' ');

    let matches = 0;
    userWords.forEach(word => {
      if (word.length > 3 && entryWords.some(entryWord => entryWord.includes(word))) {
        matches++;
      }
    });

    return matches / userWords.length;
  }

  /**
   * Get topic-specific help content
   */
  private getTopicSpecificHelp(topic: string): string {
    const helpMap: { [key: string]: string } = {
      'qr_scanning': this.knowledgeBase.get('qr_scanning') || 'QR scanning help',
      'attendance': this.knowledgeBase.get('attendance_check_guide') || 'Attendance help',
      'biometric': this.knowledgeBase.get('biometric_verification') || 'Biometric help',
      'location': this.knowledgeBase.get('location_tracking') || 'Location help',
      'notifications': this.knowledgeBase.get('notification_system') || 'Notification help',
      'profile': this.knowledgeBase.get('profile_update_guide') || 'Profile help',
      'reporting': this.knowledgeBase.get('reporting_system') || 'Reporting help'
    };

    return helpMap[topic] || 'I can help you with that topic!';
  }

  /**
   * Get topic-specific action buttons
   */
  private getTopicActions(topic: string): QuickAction[] {
    const actionMap: { [key: string]: QuickAction[] } = {
      'qr_scanning': [
        { id: 'qr_steps', label: '📋 QR Steps', action: () => {}, message: 'Show QR scanning steps' },
        { id: 'qr_issues', label: '🔧 QR Problems', action: () => {}, message: 'QR scanning issues' }
      ],
      'attendance': [
        { id: 'check_attendance', label: '📊 Check Records', action: () => {}, message: 'How to check attendance' },
        { id: 'mark_attendance', label: '✅ Mark Attendance', action: () => {}, message: 'How to mark attendance' }
      ],
      'biometric': [
        { id: 'setup_biometric', label: '⚙️ Setup', action: () => {}, message: 'Setup biometric auth' },
        { id: 'biometric_issues', label: '🔧 Issues', action: () => {}, message: 'Biometric problems' }
      ]
    };

    return actionMap[topic] || [];
  }

  /**
   * Utility methods for pattern matching
   */
  private containsKeywords(message: string, keywords: string[]): boolean {
    return keywords.some(keyword => message.includes(keyword));
  }

  /**
   * Generate unique ID for messages
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

}

// Export singleton instance
export const aiAssistantService = new AIAssistantService();
