import React, { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { useTranslation } from "react-i18next";
import "@/styles/social-media-embeds.css";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  ExternalLink,
  Instagram,
  Twitter,
  Facebook,
  Youtube,
  Globe,
  Heart,
  MessageCircle,
  Share2,
  Calendar,
  Sparkles,
  RefreshCw,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import SocialMediaSkeleton from "./SocialMediaSkeleton";

// Facebook SDK type declaration
declare global {
  interface Window {
    FB?: {
      init: (params: any) => void;
      XFBML: {
        parse: () => void;
      };
    };
  }
}

interface SocialMediaSettings {
  enabled: boolean;
  platforms: {
    instagram?: {
      enabled: boolean;
      username: string;
      embed_code?: string;
    };
    twitter?: {
      enabled: boolean;
      username: string;
      embed_code?: string;
    };
    facebook?: {
      enabled: boolean;
      page_url: string;
      embed_code?: string;
    };
    youtube?: {
      enabled: boolean;
      channel_url: string;
      channel_id?: string;
      embed_code?: string;
    };
    website?: {
      enabled: boolean;
      url: string;
      rss_feed?: string;
    };
  };
  refresh_interval_minutes: number;
  show_engagement_stats: boolean;
}

interface SocialPost {
  id: string;
  platform: string;
  content: string;
  image_url?: string;
  video_url?: string;
  post_url: string;
  likes_count?: number;
  comments_count?: number;
  shares_count?: number;
  posted_at: string;
  author_name: string;
  author_avatar?: string;
}

// Helper function to create YouTube embed template
const createYouTubeEmbedTemplate = (channelId: string): string => {
  try {
    // Validate channel ID format (should start with UC and be 24 characters)
    if (!channelId || !channelId.startsWith('UC') || channelId.length !== 24) {
      throw new Error('Invalid channel ID format');
    }

    // Convert channel ID to uploads playlist ID (UC -> UU)
    const uploadsPlaylistId = 'UU' + channelId.substring(2);

    // Create 6 separate YouTube video embeds for the most recent videos
    return `
      <div class="youtube-videos-grid">
        <div class="youtube-video-item">
          <iframe
            src="https://www.youtube-nocookie.com/embed?listType=playlist&list=${uploadsPlaylistId}&index=1&modestbranding=1&showinfo=0&rel=0&controls=1&iv_load_policy=3&cc_load_policy=0&disablekb=1&fs=1&playsinline=1"
            title="Most Recent Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            class="youtube-video-iframe">
          </iframe>
        </div>

        <div class="youtube-video-item">
          <iframe
            src="https://www.youtube-nocookie.com/embed?listType=playlist&list=${uploadsPlaylistId}&index=2&modestbranding=1&showinfo=0&rel=0&controls=1&iv_load_policy=3&cc_load_policy=0&disablekb=1&fs=1&playsinline=1"
            title="Second Most Recent Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            class="youtube-video-iframe">
          </iframe>
        </div>

        <div class="youtube-video-item">
          <iframe
            src="https://www.youtube-nocookie.com/embed?listType=playlist&list=${uploadsPlaylistId}&index=3&modestbranding=1&showinfo=0&rel=0&controls=1&iv_load_policy=3&cc_load_policy=0&disablekb=1&fs=1&playsinline=1"
            title="Third Most Recent Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            class="youtube-video-iframe">
          </iframe>
        </div>

        <div class="youtube-video-item">
          <iframe
            src="https://www.youtube-nocookie.com/embed?listType=playlist&list=${uploadsPlaylistId}&index=4&modestbranding=1&showinfo=0&rel=0&controls=1&iv_load_policy=3&cc_load_policy=0&disablekb=1&fs=1&playsinline=1"
            title="Fourth Most Recent Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            class="youtube-video-iframe">
          </iframe>
        </div>

        <div class="youtube-video-item">
          <iframe
            src="https://www.youtube-nocookie.com/embed?listType=playlist&list=${uploadsPlaylistId}&index=5&modestbranding=1&showinfo=0&rel=0&controls=1&iv_load_policy=3&cc_load_policy=0&disablekb=1&fs=1&playsinline=1"
            title="Fifth Most Recent Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            class="youtube-video-iframe">
          </iframe>
        </div>

        <div class="youtube-video-item">
          <iframe
            src="https://www.youtube-nocookie.com/embed?listType=playlist&list=${uploadsPlaylistId}&index=6&modestbranding=1&showinfo=0&rel=0&controls=1&iv_load_policy=3&cc_load_policy=0&disablekb=1&fs=1&playsinline=1"
            title="Sixth Most Recent Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            class="youtube-video-iframe">
          </iframe>
        </div>
      </div>
    `;
  } catch (error) {
    console.error('Error creating YouTube embed:', error);
    return `
      <div class="youtube-error-display">
        <div class="youtube-error-content">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="#ef4444">
            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
          </svg>
          <h3>YouTube Configuration Error</h3>
          <p>Please check your Channel ID in admin settings</p>
          <small>Channel ID should start with "UC" and be 24 characters long</small>
        </div>
      </div>
    `;
  }
};

// Create safer Facebook embed with error handling
const createSafeFacebookEmbed = (embedCode: string, pageUrl?: string) => {
  try {
    // Priority 1: If we have a proper iframe embed code, use it
    if (embedCode && embedCode.trim() && embedCode.includes('<iframe')) {
      // Clean and validate the embed code
      let safeEmbedCode = embedCode
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
        .replace(/on\w+="[^"]*"/g, '') // Remove event handlers
        .replace(/javascript:/gi, ''); // Remove javascript: URLs

      // Add fallback div
      if (!safeEmbedCode.includes('<div id="fb-root">')) {
        safeEmbedCode = '<div id="fb-root"></div>' + safeEmbedCode;
      }

      return safeEmbedCode;
    }

    // Priority 2: If we have embed code with fb-page, ensure it's properly formatted
    if (embedCode && embedCode.includes('fb-page')) {
      // Add Facebook SDK div if not present
      let safeEmbedCode = embedCode;

      // Ensure proper data attributes
      if (!safeEmbedCode.includes('data-adapt-container-width="true"')) {
        safeEmbedCode = safeEmbedCode.replace(
          'class="fb-page"',
          'class="fb-page" data-adapt-container-width="true"'
        );
      }

      // Add fallback div
      if (!safeEmbedCode.includes('<div id="fb-root">')) {
        safeEmbedCode = '<div id="fb-root"></div>' + safeEmbedCode;
      }

      return safeEmbedCode;
    }

    // Priority 3: If we have a page URL, create a Facebook page plugin
    if (pageUrl && pageUrl.includes('facebook.com')) {
      return `
        <div id="fb-root"></div>
        <div class="fb-page"
             data-href="${pageUrl}"
             data-tabs="timeline"
             data-width="500"
             data-height="600"
             data-small-header="false"
             data-adapt-container-width="true"
             data-hide-cover="false"
             data-show-facepile="false">
          <blockquote cite="${pageUrl}" class="fb-xfbml-parse-ignore">
            <a href="${pageUrl}" target="_blank" rel="noopener noreferrer">Visit our Facebook page</a>
          </blockquote>
        </div>
      `;
    }

    // Priority 4: If embedCode looks like a URL, try to use it
    if (embedCode && embedCode.includes('facebook.com')) {
      return `
        <div id="fb-root"></div>
        <div class="fb-page"
             data-href="${embedCode}"
             data-tabs="timeline"
             data-width="500"
             data-height="600"
             data-small-header="false"
             data-adapt-container-width="true"
             data-hide-cover="false"
             data-show-facepile="false">
          <blockquote cite="${embedCode}" class="fb-xfbml-parse-ignore">
            <a href="${embedCode}" target="_blank" rel="noopener noreferrer">Visit our Facebook page</a>
          </blockquote>
        </div>
      `;
    }

    // Fallback: return the original embed code if it exists
    if (embedCode && embedCode.trim()) {
      return embedCode;
    }

    // No valid content found
    return `
      <div class="facebook-error-display p-4 text-center">
        <div class="facebook-error-content">
          <h3 class="text-lg font-semibold mb-2">Facebook Feed Not Configured</h3>
          <p class="text-gray-600 mb-4">Please configure your Facebook page URL or embed code in the admin settings.</p>
          ${pageUrl ? `<a href="${pageUrl}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">Visit our Facebook page</a>` : ''}
        </div>
      </div>
    `;
  } catch (error) {
    console.error('Error creating Facebook embed:', error);
    return `
      <div class="facebook-error-display p-4 text-center">
        <div class="facebook-error-content">
          <h3 class="text-lg font-semibold mb-2 text-red-600">Facebook Feed Error</h3>
          <p class="text-gray-600 mb-4">Unable to load Facebook content at this time.</p>
          ${pageUrl ? `<a href="${pageUrl}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">Visit our Facebook page</a>` : ''}
        </div>
      </div>
    `;
  }
};

export default function SocialMediaFeed() {
  const { profile } = useAuth();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [settings, setSettings] = useState<SocialMediaSettings | null>(null);
  const [posts, setPosts] = useState<SocialPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [embedsLoading, setEmbedsLoading] = useState<Record<string, boolean>>({});
  const [selectedPlatform, setSelectedPlatform] = useState<string>("all");
  const [fbSdkLoaded, setFbSdkLoaded] = useState(false);

  // Initialize Facebook SDK
  useEffect(() => {
    const initFacebookSDK = () => {
      // Suppress Facebook SDK console errors
      const originalConsoleError = console.error;
      const originalConsoleWarn = console.warn;

      const suppressFacebookErrors = (originalMethod: any) => (...args: any[]) => {
        const message = args[0]?.toString() || '';

        // Suppress known Facebook SDK errors that don't affect functionality
        if (
          message.includes('DataStore.get: namespace is required') ||
          message.includes('Could not find element') ||
          message.includes('ErrorUtils caught an error') ||
          message.includes('DOMSubtreeModified') ||
          message.includes('fburl.com/debugjs') ||
          (args[0]?.extra && args[0]?.hash) // Facebook error object structure
        ) {
          return; // Suppress these errors
        }

        // Allow all other errors through
        originalMethod.apply(console, args);
      };

      // Override console methods to suppress Facebook errors
      console.error = suppressFacebookErrors(originalConsoleError);
      console.warn = suppressFacebookErrors(originalConsoleWarn);

      // Check if Facebook SDK is already loaded
      if (window.FB) {
        setFbSdkLoaded(true);
        return;
      }

      // Load Facebook SDK
      const script = document.createElement('script');
      script.async = true;
      script.defer = true;
      script.crossOrigin = 'anonymous';
      script.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v18.0';
      script.onload = () => {
        if (window.FB) {
          window.FB.init({
            xfbml: true,
            version: 'v18.0'
          });
          setFbSdkLoaded(true);
        }
      };
      script.onerror = () => {
        console.warn('Failed to load Facebook SDK');
      };
      document.head.appendChild(script);
    };

    initFacebookSDK();
  }, []);

  // Fetch social media settings
  useEffect(() => {
    fetchSocialMediaSettings();
  }, []);

  const fetchSocialMediaSettings = async () => {
    if (!profile?.school_id) return;

    try {
      const { data, error } = await supabase
        .from("school_settings")
        .select("social_media_integration")
        .eq("school_id", profile.school_id)
        .single();

      if (error && error.code !== "PGRST116") {
        throw error;
      }

      if (data?.social_media_integration) {
        setSettings(data.social_media_integration as SocialMediaSettings);
      } else {
        // Default settings if not configured
        setSettings({
          enabled: false,
          platforms: {},
          refresh_interval_minutes: 30,
          show_engagement_stats: true,
        });
      }
    } catch (error) {
      console.error("Error fetching social media settings:", error);
      toast({
        title: t("common.error"),
        description: "Failed to load social media settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchSocialPosts = async () => {
    if (!settings?.enabled) return;

    setRefreshing(true);
    try {
      // Check if any platforms have embed codes for direct embedding
      const hasEmbedCodes = Object.values(settings.platforms).some(
        platform => platform.enabled && (platform as any).embed_code
      );

      if (hasEmbedCodes) {
        // If embed codes are available, we'll use those for direct embedding
        // This will be handled in the render section
        setPosts([]);
      } else {
        // No mock posts - show empty state when no embed codes are provided
        setPosts([]);
      }
    } catch (error) {
      console.error("Error fetching social posts:", error);
      toast({
        title: t("common.error"),
        description: "Failed to load social media posts",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (settings?.enabled) {
      fetchSocialPosts();
    }
  }, [settings]);

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "instagram":
        return <Instagram className="w-4 h-4" />;
      case "twitter":
        return <Twitter className="w-4 h-4" />;
      case "facebook":
        return <Facebook className="w-4 h-4" />;
      case "youtube":
        return <Youtube className="w-4 h-4" />;
      default:
        return <Globe className="w-4 h-4" />;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "instagram":
        return "bg-gradient-to-r from-purple-500 to-pink-500";
      case "twitter":
        return "bg-blue-500";
      case "facebook":
        return "bg-blue-600";
      case "youtube":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return t("social.justNow");
    if (diffInHours < 24) return t("social.hoursAgo", { count: diffInHours });
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return t("social.daysAgo", { count: diffInDays });
    return date.toLocaleDateString();
  };

  const filteredPosts = selectedPlatform === "all"
    ? posts
    : posts.filter(post => post.platform === selectedPlatform);



  if (loading) {
    return <SocialMediaSkeleton />;
  }

  // Check if social media is enabled and has any configured platforms with content
  const hasConfiguredPlatforms = settings?.enabled && settings?.platforms &&
    Object.values(settings.platforms).some(platform => {
      if (!platform.enabled) return false;
      // Check if platform has content (embed_code, page_url for Facebook, or channel_id for YouTube)
      return (platform as any).embed_code ||
             (platform as any).page_url ||
             (platform as any).channel_id;
    });

  // Don't show anything if no platforms are configured - students shouldn't know this feature exists
  if (!hasConfiguredPlatforms) {
    return null;
  }

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0 w-full max-w-full overflow-hidden">
      {/* Enhanced Responsive Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-pink-900/20 p-4 sm:p-6 border border-blue-100 dark:border-blue-800">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5"></div>
        <div className="absolute top-0 right-0 w-20 h-20 sm:w-32 sm:h-32 bg-gradient-to-bl from-blue-200/20 to-transparent rounded-full -translate-y-10 translate-x-10 sm:-translate-y-16 sm:translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-tr from-purple-200/20 to-transparent rounded-full translate-y-8 -translate-x-8 sm:translate-y-12 sm:-translate-x-12"></div>

        <div className="relative z-10 flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="relative flex-shrink-0">
              <div className="p-2 sm:p-3 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-lg sm:rounded-xl shadow-lg">
                <Sparkles className="w-5 h-5 sm:w-7 sm:h-7 text-white" />
              </div>
              {/* Floating particles */}
              <div className="absolute -top-1 -right-1 w-2 h-2 sm:w-3 sm:h-3 bg-yellow-400 rounded-full animate-bounce"></div>
              <div className="absolute -bottom-1 -left-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full animate-pulse"></div>
            </div>
            <div className="min-w-0 flex-1">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent break-words">
                {t("social.schoolUpdates")}
              </h2>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 flex items-center space-x-2 mt-1">
                <span className="break-words">{t("social.stayConnected")}</span>
                <div className="flex space-x-1 flex-shrink-0">
                  <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  <div className="w-1 h-1 bg-pink-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                </div>
              </div>
            </div>
          </div>
          <Button
            onClick={fetchSocialPosts}
            disabled={refreshing}
            variant="outline"
            size="sm"
            className="hidden sm:flex items-center space-x-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-white/20 hover:bg-white dark:hover:bg-gray-700 shadow-lg hover:shadow-xl transition-all duration-200 flex-shrink-0 w-auto justify-center sm:justify-start"
          >
            <RefreshCw className={cn("w-4 h-4", refreshing && "animate-spin")} />
            <span>{t("social.refresh")}</span>
          </Button>
        </div>
      </div>

      {/* Responsive Platform Filter */}
      <div className="flex flex-wrap gap-2 sm:gap-3 justify-center sm:justify-start">
        <Button
          variant={selectedPlatform === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedPlatform("all")}
          className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-3 sm:px-4 py-2 min-w-0 flex-shrink-0"
        >
          <Globe className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
          <span>{t("social.all")}</span>
        </Button>
        {/* Custom platform order: youtube, facebook, twitter, instagram, website */}
        {["youtube", "facebook", "twitter", "instagram", "website"].map((platform) => {
          const config = settings.platforms[platform as keyof typeof settings.platforms];
          if (!config || !config.enabled) return null;
          return (
            <Button
              key={platform}
              variant={selectedPlatform === platform ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedPlatform(platform)}
              className="flex items-center space-x-0 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-4 py-2 min-w-0 flex-shrink-0"
            >
              <div className="flex-shrink-0">
                {getPlatformIcon(platform)}
              </div>
              <span className="capitalize hidden sm:inline ml-2 text-xs sm:text-sm break-words">{platform}</span>
            </Button>
          );
        })}
      </div>

      {/* Posts Feed */}
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedPlatform}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="grid gap-4 sm:gap-6 w-full max-w-full"
        >
          {/* Enhanced Native-Style Embed Feeds */}
          {/* Custom platform order: youtube, facebook, twitter, instagram, website */}
          {["youtube", "facebook", "twitter", "instagram", "website"].map((platform) => {
            const config = settings.platforms[platform as keyof typeof settings.platforms];
            if (!config) return null;

            // Show if enabled and has content (embed_code, page_url for Facebook, or channel_id for YouTube)
            const hasContent = (config as any).embed_code ||
                              (platform === "facebook" && (config as any).page_url) ||
                              (platform === "youtube" && (config as any).channel_id);
            if (!config.enabled || !hasContent) return null;
            if (selectedPlatform !== "all" && selectedPlatform !== platform) return null;

            return (
              <motion.div
                key={`embed-${platform}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className={cn(
                  "border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50",
                  "social-media-card gradient-border w-full max-w-full",
                  `platform-${platform}`,
                  platform === "youtube" ? "overflow-visible" : "overflow-hidden"
                )}>
                  {/* Enhanced Content Container */}
                  <CardContent className="p-0">
                    <div className="relative">
                      {/* Decorative gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/10 dark:to-gray-900/10 pointer-events-none z-10"></div>

                      {/* Custom styled embed container */}
                      <div className={cn(
                        "w-full relative max-w-full",
                        // Platform-specific styling and heights
                        platform === "facebook" && "facebook-embed-container min-h-[400px] overflow-hidden",
                        platform === "twitter" && "twitter-embed-container min-h-[400px] overflow-hidden",
                        platform === "youtube" && "youtube-embed-container min-h-auto overflow-visible"
                      )}>
                        {/* Loading skeleton */}
                        {embedsLoading[platform] && (
                          <div className="absolute inset-0 z-20 bg-white dark:bg-gray-900 flex items-center justify-center">
                            <div className="text-center space-y-4">
                              <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                              <p className="text-sm text-muted-foreground">Loading {platform} feed...</p>
                            </div>
                          </div>
                        )}

                        {/* YouTube Custom Responsive Template */}
                        {platform === "youtube" && (config as any).channel_id ? (
                          <div
                            className="w-full youtube-responsive-wrapper"
                            style={{ height: 'auto', minHeight: 'auto' }}
                            dangerouslySetInnerHTML={{ __html: createYouTubeEmbedTemplate((config as any).channel_id) }}
                            onLoad={() => setEmbedsLoading(prev => ({ ...prev, [platform]: false }))}
                          />
                        ) : platform === "facebook" ? (
                          /* Enhanced Facebook embed with SDK support */
                          <div
                            className="w-full h-full social-content max-w-full overflow-hidden"
                            dangerouslySetInnerHTML={{
                              __html: createSafeFacebookEmbed(
                                (config as any).embed_code || '',
                                (config as any).page_url
                              )
                            }}
                            onLoad={() => {
                              setEmbedsLoading(prev => ({ ...prev, [platform]: false }));
                              // Re-parse Facebook widgets if SDK is loaded
                              if (window.FB && fbSdkLoaded) {
                                setTimeout(() => {
                                  try {
                                    window.FB.XFBML.parse();
                                  } catch (e) {
                                    console.warn('Facebook XFBML parse error:', e);
                                  }
                                }, 100);
                              }
                            }}
                          />
                        ) : platform !== "youtube" ? (
                          /* Enhanced embed wrapper for other platforms */
                          <div
                            className="w-full h-full social-content max-w-full overflow-hidden"
                            dangerouslySetInnerHTML={{ __html: (config as any).embed_code || '' }}
                            onLoad={() => setEmbedsLoading(prev => ({ ...prev, [platform]: false }))}
                          />
                        ) : (
                          /* Fallback for YouTube without channel URL */
                          <div
                            className="w-full h-full social-content max-w-full overflow-hidden"
                            dangerouslySetInnerHTML={{ __html: (config as any).embed_code || '' }}
                            onLoad={() => setEmbedsLoading(prev => ({ ...prev, [platform]: false }))}
                          />
                        )}

                        {/* Decorative corner accents */}
                        <div className="absolute top-0 left-0 w-8 h-8 bg-gradient-to-br from-primary/20 to-transparent rounded-br-full"></div>
                        <div className="absolute top-0 right-0 w-8 h-8 bg-gradient-to-bl from-primary/20 to-transparent rounded-bl-full"></div>
                      </div>

                      {/* Beautiful bottom gradient */}
                      <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-gray-50 to-transparent dark:from-gray-900 pointer-events-none"></div>
                    </div>

                    {/* Responsive Enhanced Footer */}
                    <div className="p-3 sm:p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 border-t border-gray-100 dark:border-gray-700">
                      <div className="flex items-center justify-center">
                        <div className="flex items-center space-x-2 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          <RefreshCw className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span className="hidden sm:inline">{t("social.autoRefreshing")}</span>
                          <span className="sm:hidden">{t("social.auto")}</span>
                          <a
                            href={
                              (config as any).page_url ||
                              (config as any).channel_url ||
                              (config as any).url ||
                              `https://${platform}.com/${(config as any).username?.replace('@', '') || ''}`
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-2 text-primary hover:text-primary/80 transition-colors duration-200"
                          >
                            <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4" />
                          </a>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
          {/* Only show posts if available */}
          {filteredPosts.length > 0 ? (
            filteredPosts.map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6">
                    {/* Post Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <img
                          src={post.author_avatar}
                          alt={post.author_name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="font-semibold">{post.author_name}</h4>
                            <Badge
                              variant="secondary"
                              className={cn("text-white", getPlatformColor(post.platform))}
                            >
                              {getPlatformIcon(post.platform)}
                              <span className="ml-1 capitalize">{post.platform}</span>
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{formatTimeAgo(post.posted_at)}</span>
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="text-muted-foreground hover:text-primary"
                      >
                        <a href={post.post_url} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </Button>
                    </div>

                    {/* Post Content */}
                    <div className="space-y-4">
                      <p className="text-sm leading-relaxed">{post.content}</p>
                      
                      {/* Post Media */}
                      {post.image_url && (
                        <div className="rounded-lg overflow-hidden">
                          <img
                            src={post.image_url}
                            alt="Post content"
                            className="w-full h-64 object-cover hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                      )}

                      {/* Engagement Stats */}
                      {settings.show_engagement_stats && (
                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                            {post.likes_count && (
                              <div className="flex items-center space-x-1">
                                <Heart className="w-4 h-4" />
                                <span>{post.likes_count}</span>
                              </div>
                            )}
                            {post.comments_count && (
                              <div className="flex items-center space-x-1">
                                <MessageCircle className="w-4 h-4" />
                                <span>{post.comments_count}</span>
                              </div>
                            )}
                            {post.shares_count && (
                              <div className="flex items-center space-x-1">
                                <Share2 className="w-4 h-4" />
                                <span>{post.shares_count}</span>
                              </div>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            asChild
                            className="text-primary hover:text-primary/80"
                          >
                            <a href={post.post_url} target="_blank" rel="noopener noreferrer">
                              {t("social.viewPost")}
                            </a>
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          ) : null}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
