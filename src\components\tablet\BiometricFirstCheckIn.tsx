import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Fingerprint,
  CheckCircle,
  AlertTriangle,
  User,
  Clock,
  RotateCcw,
  Loader2,
  Shield
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { authenticateWithBiometricFirst, getDeviceInfo, updateDeviceLastSeen } from "@/lib/webauthn";

interface BiometricFirstCheckInProps {
  roomId: string;
  schoolId: string;
  onSuccess?: (studentId: string, studentName: string) => void;
  onError?: (error: string) => void;
}

interface StudentProfile {
  user_id: string;
  email: string;
  name: string;
  school_id: string;
}

export default function BiometricFirstCheckIn({
  roomId,
  schoolId,
  onSuccess,
  onError
}: BiometricFirstCheckInProps) {
  const [step, setStep] = useState<'ready' | 'authenticating' | 'success' | 'error'>('ready');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [studentProfile, setStudentProfile] = useState<StudentProfile | null>(null);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [lastCheckIn, setLastCheckIn] = useState<string | null>(null);

  const { t } = useTranslation();

  useEffect(() => {
    const info = getDeviceInfo();
    setDeviceInfo(info);
    
    // Update device last seen
    updateDeviceLastSeen(info.deviceId);
  }, []);

  const resetForm = () => {
    setStep('ready');
    setIsLoading(false);
    setError(null);
    setStudentProfile(null);
    setLastCheckIn(null);
  };

  const authenticateWithBiometric = async () => {
    if (!deviceInfo) {
      setError(t("tablet.biometricCheckIn.errors.deviceInfoNotAvailable"));
      return;
    }

    setIsLoading(true);
    setError(null);
    setStep('authenticating');

    try {
      // Authenticate using biometric-first approach
      const { credential, student } = await authenticateWithBiometricFirst(deviceInfo.deviceId);

      if (!student.success) {
        throw new Error(student.error || t("tablet.biometricCheckIn.errors.failedToIdentifyStudent"));
      }

      // Verify student belongs to the correct school
      if (student.school_id !== schoolId) {
        throw new Error(t("tablet.biometricCheckIn.errors.studentNotBelongToSchool"));
      }

      // Set student profile
      const profile: StudentProfile = {
        user_id: student.student_id,
        email: student.email,
        name: student.name,
        school_id: student.school_id
      };

      setStudentProfile(profile);

      // Record attendance
      await recordAttendance(profile);

      // Show success
      setStep('success');

      // Call success callback
      if (onSuccess) {
        onSuccess(profile.user_id, profile.name);
      }

      // Auto-reset after 5 seconds
      setTimeout(() => {
        resetForm();
      }, 5000);

    } catch (err: any) {
      console.error('Biometric authentication error:', err);

      let errorMessage = t("tablet.biometricCheckIn.errors.authenticationFailed");

      if (err.message?.includes('No biometric credentials found')) {
        errorMessage = t("tablet.biometricCheckIn.errors.noBiometricsFound");
      } else if (err.message?.includes('Student not found')) {
        errorMessage = t("tablet.biometricCheckIn.errors.studentNotRecognized");
      } else if (err.message?.includes('not belong to this school')) {
        errorMessage = t("tablet.biometricCheckIn.errors.accessDenied");
      } else if (err.message?.includes('User cancelled') || err.name === 'NotAllowedError') {
        errorMessage = t("tablet.biometricCheckIn.errors.authenticationCancelled");
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      setStep('error');

      // Call error callback
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const recordAttendance = async (profile: StudentProfile) => {
    try {
      const now = new Date();
      const today = now.toISOString().split('T')[0];

      // Check if student already checked in today
      const { data: existingAttendance, error: checkError } = await supabase
        .from("attendance_records")
        .select("*")
        .eq("student_id", profile.user_id)
        .eq("room_id", roomId)
        .gte("timestamp", `${today}T00:00:00.000Z`)
        .lt("timestamp", `${today}T23:59:59.999Z`);

      if (checkError) throw checkError;

      if (existingAttendance && existingAttendance.length > 0) {
        // Already checked in today
        setLastCheckIn(new Date(existingAttendance[0].timestamp).toLocaleTimeString());
        return;
      }

      // Create attendance record
      const { error } = await supabase
        .from("attendance_records")
        .insert({
          student_id: profile.user_id,
          room_id: roomId,
          school_id: schoolId,
          timestamp: now.toISOString(),
          device_info: `${deviceInfo.deviceName} (${deviceInfo.deviceId})`,
          verification_method: "biometric_first",
          status: "present",
          created_at: now.toISOString(),
        });

      if (error) throw error;

    } catch (err) {
      console.error('Error recording attendance:', err);
      throw new Error(t("tablet.biometricCheckIn.errors.attendanceRecordFailed"));
    }
  };

  const getStepContent = () => {
    switch (step) {
      case 'ready':
        return (
          <div className="text-center space-y-6">
            <div className="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
              <Fingerprint className="w-12 h-12 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">{t("tablet.biometricCheckIn.title")}</h3>
              <p className="text-muted-foreground">
                {t("tablet.biometricCheckIn.description")}
              </p>
            </div>
            <Button
              onClick={authenticateWithBiometric}
              disabled={isLoading}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
            >
              <Fingerprint className="w-5 h-5 mr-2" />
              {t("tablet.biometricCheckIn.startButton")}
            </Button>
            <p className="text-sm text-muted-foreground">
              {t("tablet.biometricCheckIn.registrationNote")}
            </p>
          </div>
        );

      case 'authenticating':
        return (
          <div className="text-center space-y-6">
            <div className="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
              <div className="animate-pulse">
                <Fingerprint className="w-12 h-12 text-blue-600" />
              </div>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Authenticating...</h3>
              <p className="text-muted-foreground">
                Please complete the biometric verification
              </p>
            </div>
            <div className="flex items-center justify-center">
              <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
            </div>
          </div>
        );

      case 'success':
        return (
          <div className="text-center space-y-6">
            <div className="w-24 h-24 mx-auto bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-12 h-12 text-green-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-green-900 mb-2">Check-In Successful!</h3>
              {studentProfile && (
                <div className="space-y-2">
                  <p className="text-lg font-medium">{studentProfile.name}</p>
                  <p className="text-muted-foreground">{studentProfile.email}</p>
                  {lastCheckIn ? (
                    <Badge variant="secondary" className="mt-2">
                      {t("tablet.biometricCheckIn.success.alreadyCheckedIn", { time: lastCheckIn })}
                    </Badge>
                  ) : (
                    <Badge variant="default" className="mt-2">
                      {t("tablet.biometricCheckIn.success.attendanceRecorded", { time: new Date().toLocaleTimeString() })}
                    </Badge>
                  )}
                </div>
              )}
            </div>
            <Button onClick={resetForm} variant="outline">
              {t("tablet.biometricCheckIn.success.checkInAnotherStudent")}
            </Button>
          </div>
        );

      case 'error':
        return (
          <div className="text-center space-y-6">
            <div className="w-24 h-24 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-12 h-12 text-red-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-red-900 mb-2">{t("tablet.biometricCheckIn.errors.checkInFailed")}</h3>
              <Alert variant="destructive" className="text-left">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </div>
            <div className="flex gap-3 justify-center">
              <Button onClick={resetForm} variant="outline">
                <RotateCcw className="w-4 h-4 mr-2" />
                {t("tablet.biometricCheckIn.errors.tryAgain")}
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Shield className="w-5 h-5" />
          {t("tablet.biometricCheckIn.title")}
        </CardTitle>
        <CardDescription>
          {t("tablet.biometricCheckIn.subtitle")}
        </CardDescription>
      </CardHeader>
      <CardContent className="py-8">
        {getStepContent()}
      </CardContent>
    </Card>
  );
}
