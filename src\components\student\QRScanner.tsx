import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "@/lib/utils/toast";
import { Camera, Fingerprint, Key, Check, Loader2 } from "lucide-react";
import { toast as sonnerToast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { startAuthentication, isWebAuthnAvailable, isBiometricOnlySupported, canPerformBiometricAuth } from "@/lib/webauthn";
import { Input } from "@/components/ui/input";
import {
  getDeviceFingerprint,
  isLocationSpoofed,
  checkConcurrentSessions,
  isDeviceConsistent,
  hasReachedRateLimit,
  calculateDistance,
  createLocationAlert,
} from "@/lib/utils/security";
import { createLocalizedNotification } from "@/lib/utils/notification-localization";
import { useTranslation } from "react-i18next";
import { createUTCTimestamp } from "@/lib/utils/timezone";

// Define strict types for our GeoJSON Point
type GeoJSONPoint = {
  type: "Point";
  coordinates: [number, number]; // [longitude, latitude]
};

type LocationState = GeoJSONPoint | null;

interface RoomLocation {
  latitude: number;
  longitude: number;
  radius_meters: number;
}

export default function QRScanner() {
  const [isScanning, setIsScanning] = useState(false);
  const [needsVerification, setNeedsVerification] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [verificationType, setVerificationType] = useState<
    "biometric" | "pin" | null
  >(null);
  const [pin, setPin] = useState("");
  const [success, setSuccess] = useState(false);
  const [location, setLocation] = useState<LocationState>(null);
  const { profile } = useAuth();
  const { t } = useTranslation();

  // In a real app, this would be decoded from the QR code
  // For testing, we'll fetch a real room from the database
  const [roomData, setRoomData] = useState<{ id: string; name: string } | null>(
    null
  );

  // Fetch an available room for testing
  useEffect(() => {
    const fetchRoom = async () => {
      const { data: rooms, error } = await supabase
        .from("rooms")
        .select("id, name")
        .limit(1);

      if (error) {
        console.error("Error fetching room:", error);
        return;
      }

      if (rooms && rooms.length > 0) {
        setRoomData(rooms[0]);
      }
    };

    fetchRoom();
  }, []);

  // Simulate QR scanning
  const handleStartScan = () => {
    setIsScanning(true);
    setSuccess(false);

    // Simulate a delay for "scanning"
    setTimeout(() => {
      setIsScanning(false);

      if (!roomData) {
        toast.translateError(
          t,
          "common.error",
          "student.qr.noValidRoom"
        );
        return;
      }

      setNeedsVerification(true);
    }, 2000);
  };

  const getBrowserSettingsInstructions = () => {
    const isChrome = navigator.userAgent.includes("Chrome");
    const isFirefox = navigator.userAgent.includes("Firefox");
    const isSafari =
      navigator.userAgent.includes("Safari") &&
      !navigator.userAgent.includes("Chrome");
    const isEdge = navigator.userAgent.includes("Edg");

    if (isChrome) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Click the lock icon 🔒 in the address bar</li>
          <li>Click "Site settings"</li>
          <li>Find "Location" and change it to "Allow"</li>
          <li>Refresh this page</li>
        </ol>
      );
    } else if (isEdge) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Click the lock icon 🔒 in the address bar</li>
          <li>Click "Site permissions"</li>
          <li>Find "Location" and change it to "Allow"</li>
          <li>Refresh this page</li>
        </ol>
      );
    } else if (isFirefox) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Click the lock icon 🔒 in the address bar</li>
          <li>Click "More Information"</li>
          <li>Go to "Permissions"</li>
          <li>
            Find "Access Your Location" and remove the setting or set to "Allow"
          </li>
          <li>Refresh this page</li>
        </ol>
      );
    } else if (isSafari) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Click Safari menu {"->"} Preferences</li>
          <li>Go to "Privacy & Security"</li>
          <li>Under "Website tracking", click "Manage Website Settings"</li>
          <li>Find this website and set Location to "Allow"</li>
          <li>Refresh this page</li>
        </ol>
      );
    } else {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>
            Look for the lock icon 🔒 or site settings in your browser's address
            bar
          </li>
          <li>Find the location permission settings</li>
          <li>Allow location access for this site</li>
          <li>Refresh this page</li>
        </ol>
      );
    }
  };

  const showLocationInstructions = () => {
    toast({
      title: "Location Access Required",
      description: (
        <div className="flex flex-col gap-3">
          <p className="font-medium">
            Please enable location access in your browser:
          </p>
          {getBrowserSettingsInstructions()}
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="w-fit mt-2"
          >
            Refresh Page
          </Button>
        </div>
      ),
      variant: "destructive",
      duration: 10000, // Show for 10 seconds to give time to read
    });
  };

  const checkLocationPermission = async (): Promise<
    "granted" | "prompt" | "denied"
  > => {
    try {
      // First check if geolocation is supported
      if (!navigator.geolocation) {
        toast.translateError(
          t,
          "student.qr.locationRequired",
          "student.qr.browserNotSupported"
        );
        return "denied";
      }

      // Check permission status
      const permission = await navigator.permissions.query({
        name: "geolocation",
      });

      if (permission.state === "denied") {
        showLocationInstructions();
        return "denied";
      } else if (permission.state === "prompt") {
        // Test location access to trigger the browser's permission prompt
        try {
          await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 5000,
            });
          });
          return "granted";
        } catch (error) {
          if (
            error instanceof GeolocationPositionError &&
            error.code === error.PERMISSION_DENIED
          ) {
            showLocationInstructions();
            return "denied";
          }
          // If it's a timeout or other error, we'll still consider it as 'prompt'
          return "prompt";
        }
      }

      return permission.state;
    } catch (error) {
      console.error("Error checking location permission:", error);
      return "prompt"; // Default to prompt if we can't check
    }
  };

  const handleVerificationMethod = async (method: "biometric" | "pin") => {
    // Check location permission first
    const permissionStatus = await checkLocationPermission();
    if (permissionStatus === "denied") {
      return;
    }

    setVerificationType(method);

    if (method === "biometric") {
      try {
        if (!profile) {
          throw new Error("User profile not found");
        }

        // Check if WebAuthn is available
        if (!isWebAuthnAvailable()) {
          throw new Error(
            "Biometric/security key authentication is not supported on this device or browser"
          );
        }

        // Check if user has registered biometrics
        const { data: credentials, error: credentialsError } = await supabase
          .from("biometric_credentials")
          .select("credential_id")
          .eq("user_id", profile.id)
          .single();

        if (credentialsError) {
          if (credentialsError.code === "PGRST116") {
            throw new Error(
              "Please register your biometrics in your profile settings first"
            );
          }
          console.error(
            "Error checking biometric credentials:",
            credentialsError
          );
          throw new Error(
            "Failed to check biometric credentials. Please try again."
          );
        }

        // Check if device can perform biometric authentication (more lenient for auth)
        const canAuth = await canPerformBiometricAuth(user.id);
        if (!canAuth) {
          throw new Error("Biometric authentication is not available. Please ensure you have registered biometrics and your device supports biometric authentication.");
        }

        // Start biometric authentication with biometric-only enforcement
        await startAuthentication(profile.id);

        // If authentication is successful, proceed with attendance
        await handleSuccessfulAttendance();
      } catch (error) {
        console.error("Authentication verification failed:", error);
        let errorMessage = "";

        if (error instanceof Error) {
          if (error.message.includes("NotAllowedError")) {
            errorMessage =
              "Authentication was cancelled or timed out. Please try again.";
          } else if (error.message.includes("register your biometrics")) {
            errorMessage = error.message;
          } else {
            errorMessage =
              "Authentication failed. Please try using PIN instead.";
          }
        } else {
          errorMessage =
            "Authentication failed. Please ensure your device supports biometric or security key authentication, or use PIN instead.";
        }

        toast.error(
          t("student.qr.verificationFailed"),
          {
            description: errorMessage
          }
        );
        // Reset verification type to allow user to try again
        setVerificationType(null);
      }
    }
  };

  const handlePinSubmit = async () => {
    console.log("PIN submit initiated");
    setIsSubmitting(true);

    try {
      if (!profile || !roomData) {
        console.error("Missing required data:", {
          profileExists: !!profile,
          roomDataExists: !!roomData,
        });
        toast.error(
          t("common.error"),
          {
            description: !profile
              ? t("student.qr.profileNotFound")
              : t("student.qr.roomDataNotFound")
          }
        );
        setIsSubmitting(false);
        return;
      }

      if (!profile?.pin) {
        console.log("PIN not set in profile");
        toast.translateError(
          t,
          "student.qr.pinNotSet",
          "student.qr.pinNotSetDescription"
        );
        setIsSubmitting(false);
        return;
      }

      console.log("Validating PIN...");
      // Validate PIN against the stored PIN
      if (profile.pin !== pin) {
        console.log("Invalid PIN entered");
        toast.translateError(
          t,
          "student.qr.invalidPin",
          "student.qr.invalidPinDescription"
        );
        setIsSubmitting(false);
        return;
      }

      console.log("PIN validation successful");

      // Check if student has already taken attendance today for this room
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      console.log("Checking for existing attendance record today...");
      const { data: existingAttendance, error: attendanceError } =
        await supabase
          .from("attendance_records")
          .select("id, timestamp")
          .eq("student_id", profile.id)
          .eq("room_id", roomData.id)
          .gte("timestamp", today.toISOString())
          .order("timestamp", { ascending: false })
          .limit(1);

      if (attendanceError) {
        console.error("Error checking existing attendance:", attendanceError);
      }

      if (existingAttendance && existingAttendance.length > 0) {
        console.log(
          "Student already has attendance record for today:",
          existingAttendance[0]
        );
        toast.info(
          t("student.qr.alreadyRecorded"),
          {
            description: t("student.qr.alreadyRecordedDescription", {
              roomName: roomData.name,
              time: new Date(existingAttendance[0].timestamp).toLocaleTimeString()
            })
          }
        );
        setIsSubmitting(false);
        return;
      }

      // Get device information
      console.log("Getting device fingerprint...");
      const deviceInfo = await getDeviceFingerprint();

      // Start location check in parallel with attendance recording
      console.log("Starting location check...");
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          console.log("Location obtained:", {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });

          // Create GeoJSON Point
          const geoJsonLocation = {
            type: "Point",
            coordinates: [
              Number(position.coords.longitude),
              Number(position.coords.latitude),
            ],
          };

          // Record attendance immediately
          await recordAttendance(
            profile.id,
            roomData.id,
            deviceInfo,
            geoJsonLocation,
            position
          );
        },
        (error) => {
          console.error("Location error:", error);
          // Still record attendance even if location fails
          recordAttendance(profile.id, roomData.id, deviceInfo, null, null);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        }
      );

      // Show immediate feedback that we're processing
      toast.info(
        t("student.qr.processing"),
        {
          description: t("student.qr.processingDescription")
        }
      );
    } catch (error) {
      // Error in PIN submission - keeping error for debugging
      toast.translateError(
        t,
        "common.error",
        "student.qr.pinProcessingError"
      );
      setIsSubmitting(false);
    }
  };

  const recordAttendance = async (
    studentId: string,
    roomId: string,
    deviceInfo: any,
    geoJsonLocation: any,
    position: GeolocationPosition | null
  ) => {
    // Recording attendance for student

    try {
      const now = createUTCTimestamp();
      let distance = 0;
      let isWithinRadius = true;
      let roomLocation = null;

      // If we have position data, check location
      if (position && geoJsonLocation) {
        // First try to get room location from room_locations table
        const { data: roomLocationData, error: roomLocationError } =
          await supabase
            .from("room_locations")
            .select("latitude, longitude, radius_meters")
            .eq("room_id", roomId)
            .maybeSingle();

        if (roomLocationError && roomLocationError.code !== "PGRST116") {
          console.error("Error fetching room location:", roomLocationError);
        }

        let roomOrBlockLocation = roomLocationData;

        // If no room location is set, try to get block location
        if (!roomOrBlockLocation) {
          console.log("No room location found, checking block location...");

          // Get the room's block_id
          const { data: roomData2, error: roomError } = await supabase
            .from("rooms")
            .select("block_id")
            .eq("id", roomId)
            .single();

          if (roomError) {
            console.error("Error fetching room block:", roomError);
          } else if (roomData2 && roomData2.block_id) {
            // Get the block location
            const { data: blockLocationData, error: blockLocationError } =
              await supabase
                .from("block_locations")
                .select("latitude, longitude, radius_meters")
                .eq("block_id", roomData2.block_id)
                .maybeSingle();

            if (blockLocationError && blockLocationError.code !== "PGRST116") {
              console.error(
                "Error fetching block location:",
                blockLocationError
              );
            }

            roomOrBlockLocation = blockLocationData;
          }
        }

        if (roomOrBlockLocation) {
          roomLocation = {
            latitude: roomOrBlockLocation.latitude,
            longitude: roomOrBlockLocation.longitude,
            radius_meters: roomOrBlockLocation.radius_meters,
          };

          // Calculate distance
          distance = calculateDistance(
            position.coords.latitude,
            position.coords.longitude,
            roomLocation.latitude,
            roomLocation.longitude
          );

          console.log(
            `Distance from room: ${Math.round(
              distance
            )} meters (allowed radius: ${roomLocation.radius_meters} meters)`
          );

          // Check if student is within radius
          isWithinRadius = distance <= roomLocation.radius_meters;

          // If outside radius, create alert but still record attendance
          if (!isWithinRadius && roomData) {
            try {
              await createLocationAlert(
                studentId,
                position.coords.latitude,
                position.coords.longitude,
                roomLocation.latitude,
                roomLocation.longitude,
                distance,
                roomData.name,
                roomLocation.radius_meters
              );

              // Calculate how far outside the radius the student is
              const excessDistance = Math.round(distance - roomLocation.radius_meters);
              const distanceDisplay = distance >= 1000
                ? `${(distance / 1000).toFixed(1)}km`
                : `${Math.round(distance)}m`;

              // Show warning to student
              toast.warning(
                t("student.qr.locationWarning"),
                {
                  description: t("student.qr.locationWarningDescription", {
                    distance: distanceDisplay,
                    excessDistance,
                    allowedRadius: roomLocation.radius_meters
                  })
                }
              );
            } catch (alertError) {
              console.error("Error creating location alert:", alertError);
            }
          }
        }
      }

      // Now record the attendance
      console.log("Inserting attendance record into database...");

      // Create the attendance record
      const attendanceData = {
        student_id: studentId,
        room_id: roomId,
        timestamp: now,
        device_info: deviceInfo,
        verification_method: verificationType || "pin",
        status: "present",
        location: geoJsonLocation,
        created_at: now,
      };

      console.log("Attendance data to insert:", attendanceData);

      const { data: insertData, error: recordError } = await supabase
        .from("attendance_records")
        .insert(attendanceData)
        .select()
        .single();

      if (recordError) {
        console.error("Attendance recording error:", recordError);
        toast.translateError(
          t,
          "common.error",
          "student.qr.recordAttendanceError"
        );
        setIsSubmitting(false);
        return;
      }

      console.log("Successfully inserted attendance record:", insertData);

      // Create a localized notification for the student ONLY if within radius
      // Students should not receive notifications when they violate location rules
      if (isWithinRadius) {
        try {
          const roomName = roomData?.name || "class";
          const timeString = new Date().toLocaleTimeString();

          await createLocalizedNotification({
            studentId: studentId,
            type: "attendance",
            templateKey: "attendanceRecorded",
            templateParams: [roomName, timeString],
            metadata: {
              room_name: roomName,
              verification_method: verificationType || "pin",
              distance_meters: Math.round(distance),
              within_radius: isWithinRadius,
            },
            roomNumber: roomName,
          });
          console.log("Localized student notification created successfully");
        } catch (notificationError) {
          console.error("Error creating localized notification:", notificationError);
        }
      }

      // Show success message
      toast.translateSuccess(
        t,
        "common.success",
        "student.qr.attendanceRecorded"
      );

      // Show additional success toast
      sonnerToast.success("Attendance Recorded", {
        description: `Your attendance has been successfully recorded for ${
          roomData?.name || "class"
        }.`,
      });

      // Update UI state
      setSuccess(true);
      setNeedsVerification(false);
      setIsSubmitting(false);

      // Get location with high accuracy
      let currentLocation: LocationState = null;
      let locationAttempts = 0;
      const maxAttempts = 3;

      while (!currentLocation && locationAttempts < maxAttempts) {
        try {
          if (!navigator.geolocation) {
            throw new Error(
              "Your browser does not support location services. Please use a modern browser."
            );
          }

          // Show a toast to inform user about location check
          if (locationAttempts === 0) {
            toast.info(
              t("student.qr.checkingLocation"),
              {
                description: t("student.qr.checkingLocationDescription"),
                duration: 10000
              }
            );
          }

          const position = await new Promise<GeolocationPosition>(
            (resolve, reject) => {
              navigator.geolocation.getCurrentPosition(
                resolve,
                (error) => {
                  // Handle specific geolocation errors
                  let errorMessage = "Failed to get your location. ";
                  switch (error.code) {
                    case error.TIMEOUT:
                      errorMessage +=
                        "The request timed out. Please check your internet connection and try again.";
                      break;
                    case error.POSITION_UNAVAILABLE:
                      errorMessage +=
                        "Location information is unavailable. Please ensure you have GPS enabled.";
                      break;
                    case error.PERMISSION_DENIED:
                      errorMessage +=
                        "Location permission was denied. Please enable location access in your browser settings.";
                      break;
                    default:
                      errorMessage += error.message || "Please try again.";
                  }
                  reject(new Error(errorMessage));
                },
                {
                  enableHighAccuracy: true,
                  timeout: 30000,
                  maximumAge: 0,
                }
              );
            }
          );

          // Clear any existing location check toast
          toast.info(
            t("student.qr.locationFound"),
            {
              description: t("student.qr.processingDescription")
            }
          );

          // Create GeoJSON Point object with exact format required by the database
          const locationData = {
            type: "Point",
            coordinates: [
              Number(position.coords.longitude),
              Number(position.coords.latitude),
            ],
          };

          // Validate the location data matches the database constraint
          if (
            typeof locationData !== "object" ||
            !locationData.type ||
            locationData.type !== "Point" ||
            !Array.isArray(locationData.coordinates) ||
            locationData.coordinates.length !== 2 ||
            typeof locationData.coordinates[0] !== "number" ||
            typeof locationData.coordinates[1] !== "number" ||
            isNaN(locationData.coordinates[0]) ||
            isNaN(locationData.coordinates[1])
          ) {
            throw new Error("Invalid location data format");
          }

          // Log the validated location object
          console.log("Validated location data:", JSON.stringify(locationData));

          // Store location state
          setLocation(locationData as GeoJSONPoint);
          currentLocation = locationData;

          // Check for location accuracy
          if (position.coords.accuracy > 100) {
            throw new Error(
              "Location accuracy is too low. Please ensure you have a clear view of the sky and try again."
            );
          }

          // Check for location spoofing
          if (
            await isLocationSpoofed(
              position.coords.latitude,
              position.coords.longitude
            )
          ) {
            throw new Error(
              "We detected unusual location data. Please ensure you are not using any location spoofing apps."
            );
          }

          // First try to get room location from room_locations table
          const { data: roomLocationData, error: roomLocationError } =
            await supabase
              .from("room_locations")
              .select("latitude, longitude, radius_meters")
              .eq("room_id", roomData.id)
              .maybeSingle();

          if (roomLocationError && roomLocationError.code !== "PGRST116") {
            console.error("Error fetching room location:", roomLocationError);
            throw new Error(
              "Unable to verify room location. Please try again."
            );
          }

          let roomOrBlockLocation = roomLocationData;

          // If no room location is set, try to get block location
          if (!roomOrBlockLocation) {
            console.log("No room location found, checking block location...");

            // Get the room's block_id
            const { data: roomData2, error: roomError } = await supabase
              .from("rooms")
              .select("block_id")
              .eq("id", roomData.id)
              .single();

            if (roomError) {
              console.error("Error fetching room block:", roomError);
              throw new Error("Unable to verify room block. Please try again.");
            }

            if (roomData2 && roomData2.block_id) {
              // Get the block location
              const { data: blockLocationData, error: blockLocationError } =
                await supabase
                  .from("block_locations")
                  .select("latitude, longitude, radius_meters")
                  .eq("block_id", roomData2.block_id)
                  .maybeSingle();

              if (
                blockLocationError &&
                blockLocationError.code !== "PGRST116"
              ) {
                console.error(
                  "Error fetching block location:",
                  blockLocationError
                );
                throw new Error(
                  "Unable to verify block location. Please try again."
                );
              }

              roomOrBlockLocation = blockLocationData;
            }
          }

          if (!roomOrBlockLocation) {
            throw new Error(
              "This room has not been set up for location-based attendance. Please notify your teacher to set up the location for this room or block."
            );
          }

          const roomLocation: RoomLocation = {
            latitude: roomOrBlockLocation.latitude,
            longitude: roomOrBlockLocation.longitude,
            radius_meters: roomOrBlockLocation.radius_meters,
          };

          // Calculate distance using the coordinates from our GeoJSON Point
          const distance = calculateDistance(
            position.coords.latitude,
            position.coords.longitude,
            roomLocation.latitude,
            roomLocation.longitude
          );

          console.log("Student location:", {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
          console.log("Room/Block location:", roomLocation);

          console.log(
            `Distance from room: ${Math.round(
              distance
            )} meters (allowed radius: ${roomLocation.radius_meters} meters)`
          );

          // If student is outside the geofenced area
          if (distance > roomLocation.radius_meters) {
            // Create a distance alert but don't throw an error yet
            try {
              await createLocationAlert(
                profile.id,
                position.coords.latitude,
                position.coords.longitude,
                roomLocation.latitude,
                roomLocation.longitude,
                distance,
                roomData.name,
                roomLocation.radius_meters
              );
            } catch (alertError) {
              console.error("Error creating location alert:", alertError);
            }

            // Calculate how far outside the radius the student is
            const excessDistance = Math.round(distance - roomLocation.radius_meters);
            const distanceDisplay = distance >= 1000
              ? `${(distance / 1000).toFixed(1)}km`
              : `${Math.round(distance)}m`;

            // If the distance is way too far, block the attendance
            if (distance > roomLocation.radius_meters * 2) {
              throw new Error(
                `You are ${distanceDisplay} away from your room, which is ${excessDistance}m outside the ${roomLocation.radius_meters}m allowed radius. ` +
                  `Please move closer to the room and try again.`
              );
            } else {
              // If they're close but not quite in range, warn but allow with a note
              toast.warning(
                t("common.warning"),
                {
                  description: t("student.qr.locationWarningDescription", {
                    distance: distanceDisplay,
                    excessDistance,
                    allowedRadius: roomLocation.radius_meters
                  })
                }
              );
            }
          }

          // Check for concurrent sessions
          console.log("Checking for concurrent sessions...");
          const hasConcurrentSessions = await checkConcurrentSessions(
            profile.id
          );
          console.log(
            "Concurrent sessions check result:",
            hasConcurrentSessions
          );
          if (hasConcurrentSessions) {
            toast.translateWarning(
              t,
              "common.warning",
              "student.qr.concurrentSessionWarning"
            );
          }

          // Check for rate limiting
          console.log("Checking rate limits...");
          const isRateLimited = await hasReachedRateLimit(profile.id);
          console.log("Rate limit check result:", isRateLimited);
          if (isRateLimited) {
            throw new Error(
              "You have made too many attendance attempts. Please try again in a few minutes."
            );
          }

          // Check for device consistency
          console.log("Checking device consistency...");
          const isDeviceConsistentResult = await isDeviceConsistent(
            profile.id,
            deviceInfo
          );
          console.log(
            "Device consistency check result:",
            isDeviceConsistentResult
          );
          if (!isDeviceConsistentResult) {
            toast.translateWarning(
              t,
              "common.warning",
              "student.qr.deviceChangeWarning"
            );
          }

          // Record attendance with the validated GeoJSON Point location
          console.log("Preparing to record attendance...");
          const now = createUTCTimestamp();
          const geoJsonLocation = {
            type: "Point",
            coordinates: [
              Number(position.coords.longitude),
              Number(position.coords.latitude),
            ],
          };

          console.log("Inserting attendance record with data:", {
            student_id: profile.id,
            room_id: roomData.id,
            timestamp: now,
            verification_method: verificationType || "manual",
            status: "present",
            location: geoJsonLocation,
          });

          try {
            console.log("Inserting attendance record into database...");
            // First check if the table exists
            const { data: tableCheck, error: tableError } = await supabase
              .from("attendance_records")
              .select("id")
              .limit(1);

            if (tableError && tableError.code !== "PGRST116") {
              console.error(
                "Error checking attendance_records table:",
                tableError
              );
            }

            console.log("Table check complete, proceeding with insert");

            // Create the attendance record
            const attendanceData = {
              student_id: profile.id,
              room_id: roomData.id,
              timestamp: now,
              device_info: deviceInfo,
              verification_method: verificationType || "manual",
              status: "present",
              location: geoJsonLocation,
              created_at: now,
            };

            console.log("Attendance data to insert:", attendanceData);

            const { data: insertData, error: recordError } = await supabase
              .from("attendance_records")
              .insert(attendanceData)
              .select()
              .single();

            if (recordError) {
              console.error("Attendance recording error:", recordError);
              console.error("Attempted to insert location:", geoJsonLocation);
              throw new Error("Failed to record attendance. Please try again.");
            }

            // Create a localized notification for the student ONLY if within radius
            // Students should not receive notifications when they violate location rules
            if (isWithinRadius) {
              try {
                const roomName = roomData.name;
                const timeString = new Date().toLocaleTimeString();

                await createLocalizedNotification({
                  studentId: profile.id,
                  type: "attendance",
                  templateKey: "attendanceRecorded",
                  templateParams: [roomName, timeString],
                  metadata: {
                    room_name: roomName,
                    verification_method: verificationType || "manual",
                    distance_meters: Math.round(distance),
                  },
                  roomNumber: roomName,
                });
                console.log("Localized student notification created successfully");
              } catch (notificationError) {
                console.error("Error creating localized notification:", notificationError);
              }
            }

            setSuccess(true);
            setNeedsVerification(false);

            // Show success message
            toast.translateSuccess(
              t,
              "common.success",
              "student.qr.attendanceRecorded"
            );

            // Show additional success toast
            sonnerToast.success("Attendance Recorded", {
              description: `Your attendance has been successfully recorded for ${roomData.name}.`,
            });
          } catch (insertError) {
            console.error(
              "Exception during attendance insertion:",
              insertError
            );
            throw new Error("Failed to record attendance. Please try again.");
          }
        } catch (locError) {
          locationAttempts++;
          if (locError instanceof GeolocationPositionError) {
            switch (locError.code) {
              case GeolocationPositionError.PERMISSION_DENIED:
                showLocationInstructions();
                throw new Error("Location access denied");
              case GeolocationPositionError.POSITION_UNAVAILABLE:
                if (locationAttempts === maxAttempts) {
                  throw new Error(
                    "Unable to determine your location. Please ensure you have a clear view of the sky and try again."
                  );
                }
                await new Promise((resolve) => setTimeout(resolve, 1000));
                continue;
              case GeolocationPositionError.TIMEOUT:
                if (locationAttempts === maxAttempts) {
                  throw new Error(
                    "Location request timed out. Please check your internet connection and try again."
                  );
                }
                await new Promise((resolve) => setTimeout(resolve, 1000));
                continue;
              default:
                throw new Error(
                  "Location error: Please ensure location services are enabled and try again."
                );
            }
          }
          if (locationAttempts === maxAttempts) {
            throw locError;
          }
        }
      }
    } catch (error) {
      console.error("Error recording attendance:", error);
      toast.error(
        t("student.qr.attendanceError"),
        {
          description: error instanceof Error
            ? error.message
            : t("student.qr.recordAttendanceError")
        }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderContent = () => {
    if (success) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
            <Check size={40} className="text-green-600" />
          </div>
          <h3 className="text-xl font-medium text-center">
            Attendance Recorded!
          </h3>
          <p className="text-center text-muted-foreground">
            Your attendance has been successfully recorded and sent to your
            teacher
          </p>
          <Button
            onClick={() => {
              setSuccess(false);
              setNeedsVerification(false);
            }}
            className="mt-4"
          >
            Scan Another Code
          </Button>
        </div>
      );
    }

    if (isScanning) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="w-64 h-64 border-4 border-secondary rounded-lg relative overflow-hidden">
            <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-secondary transform -translate-y-1/2 animate-pulse"></div>
          </div>
          <p className="text-center text-muted-foreground">
            Scanning QR code...
          </p>
        </div>
      );
    }

    if (needsVerification) {
      if (verificationType === "biometric") {
        return (
          <div className="flex flex-col items-center justify-center p-8 space-y-6">
            <Fingerprint size={80} className="text-secondary animate-pulse" />
            <p className="text-center text-lg font-medium">
              Use your biometric or security key to verify
            </p>
          </div>
        );
      }

      if (verificationType === "pin") {
        return (
          <div className="flex flex-col items-center justify-center p-6 space-y-4">
            <h3 className="text-lg font-medium">Enter your 6-digit PIN</h3>
            <div className="flex flex-col items-center space-y-4 w-full">
              <input
                type="password"
                className="w-full px-4 py-2 text-center text-2xl tracking-widest border rounded-md"
                maxLength={6}
                value={pin}
                onChange={(e) => setPin(e.target.value.replace(/[^0-9]/g, ""))}
                autoFocus
              />
              <Button
                onClick={handlePinSubmit}
                className="w-full bg-secondary"
                disabled={pin.length !== 6 || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Verifying
                  </>
                ) : (
                  "Verify"
                )}
              </Button>
            </div>
          </div>
        );
      }

      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-6">
          <h3 className="text-xl font-medium text-center">
            Verify Your Identity
          </h3>
          <div className="grid grid-cols-2 gap-4 w-full">
            <Button
              onClick={() => handleVerificationMethod("biometric")}
              variant="outline"
              className="h-32 flex flex-col items-center justify-center gap-2 hover:border-secondary hover:text-secondary transition-colors"
            >
              <Fingerprint size={32} />
              <span>Biometric</span>
            </Button>
            <Button
              onClick={() => handleVerificationMethod("pin")}
              variant="outline"
              className="h-32 flex flex-col items-center justify-center gap-2 hover:border-primary hover:text-primary transition-colors"
            >
              <Key size={32} />
              <span>PIN Code</span>
            </Button>
          </div>
        </div>
      );
    }

    if (isSubmitting) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <Loader2
            size={64}
            className="animate-spin loader-icon"
            data-testid="LoaderIcon"
          />
          <p className="text-center">{t("loading.recordingAttendance")}</p>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <Camera
          size={64}
          className="animate-pulse camera-icon"
          data-testid="CameraIcon"
        />
        <p className="text-center">
          {t("qrScanner.scanQrDescriptionFull")}
        </p>
        <Button
          onClick={handleStartScan}
          className="w-full bg-primary hover:bg-primary/90 transition-colors"
        >
          {t("qrScanner.startScan")}
        </Button>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-md mx-auto border-2 border-muted hover:border-muted/80 transition-colors">
      <CardHeader>
        <CardTitle>QR Code Attendance</CardTitle>
        <CardDescription>
          Scan the QR code to record your attendance
        </CardDescription>
      </CardHeader>
      <CardContent>{renderContent()}</CardContent>
    </Card>
  );
}
