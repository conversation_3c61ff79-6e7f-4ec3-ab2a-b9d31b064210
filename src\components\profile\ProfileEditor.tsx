import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, Edit, Save, User, School, AlertCircle } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/context/AuthContext";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import {
  Block,
  Room,
  School as SchoolType,
  Student,
  Teacher,
  Admin,
} from "@/lib/types";
import { fetchSchools } from "@/lib/api/schools";

// Form schemas for different user types
const studentProfileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  studentId: z.string().min(2, { message: "Student ID is required." }),
  course: z.string().min(1, { message: "Class/Grade is required." }),
  block_id: z.string().min(1, { message: "Block selection is required." }),
  room_id: z.string().min(1, { message: "Room selection is required." }),
  pin: z
    .string()
    .length(6, { message: "PIN must be 6 digits." })
    .regex(/^\d+$/, { message: "PIN must contain only numbers." }),
  school: z.string().min(1, { message: "School selection is required." }),
});

const teacherProfileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  teacherId: z.string().min(2, { message: "Teacher ID is required." }),
  department: z.string().min(1, { message: "Department is required." }),
  position: z.string().min(1, { message: "Position is required." }),
  subject: z.string().min(1, { message: "Subject is required." }),
  school: z.string().min(1, { message: "School selection is required." }),
});

const adminProfileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  adminId: z.string().min(2, { message: "Admin ID is required." }),
  position: z.string().min(2, { message: "Position is required." }),
  school: z.string().min(2, { message: "School name is required." }),
});

type StudentFormValues = z.infer<typeof studentProfileSchema>;
type TeacherFormValues = z.infer<typeof teacherProfileSchema>;
type AdminFormValues = z.infer<typeof adminProfileSchema>;

interface ProfileEditorProps {
  isSetupMode?: boolean;
  userRole: "student" | "teacher" | "admin";
}

export default function ProfileEditor({
  isSetupMode = false,
  userRole,
}: ProfileEditorProps) {
  const [isEditing, setIsEditing] = useState(isSetupMode);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [schools, setSchools] = useState<SchoolType[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null);
  const [loadingSchools, setLoadingSchools] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { toast } = useToast();
  const { profile, updateProfile } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Get the appropriate schema based on user role
  const getSchema = () => {
    switch (userRole) {
      case "student":
        return studentProfileSchema;
      case "teacher":
        return teacherProfileSchema;
      case "admin":
        return adminProfileSchema;
      default:
        return studentProfileSchema;
    }
  };

  // Get default values based on user role
  const getDefaultValues = () => {
    switch (userRole) {
      case "student":
        return {
          name: "",
          studentId: "",
          course: "",
          block_id: "",
          room_id: "",
          pin: "",
          school: "",
        };
      case "teacher":
        return {
          name: "",
          teacherId: "",
          department: "",
          position: "",
          subject: "",
          school: "",
        };
      case "admin":
        return {
          name: "",
          adminId: "",
          position: "",
          school: "",
        };
      default:
        return {};
    }
  };

  const form = useForm({
    resolver: zodResolver(getSchema()),
    defaultValues: getDefaultValues(),
  });

  // Fetch schools from schools table
  useEffect(() => {
    const getSchools = async () => {
      setLoadingSchools(true);
      try {
        const schoolsList = await fetchSchools();
        setSchools(schoolsList);

        if (schoolsList.length === 0) {
          toast({
            title: t("profile.noSchoolsFound"),
            description: t("profile.noSchoolsFoundDesc"),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error fetching schools:", error);
        toast({
          title: t("common.error"),
          description: t("profile.failedToFetchSchools"),
          variant: "destructive",
        });
      } finally {
        setLoadingSchools(false);
      }
    };

    getSchools();
  }, [toast, t]);

  // Fetch blocks and rooms for students
  useEffect(() => {
    if (userRole !== "student") return;

    // Only fetch blocks if we have a school_id
    if (!profile?.school_id) {
      setBlocks([]);
      return;
    }

    const fetchBlocks = async () => {
      try {
        const { data, error } = await supabase
          .from("blocks")
          .select("*")
          .eq("school_id", profile.school_id)
          .order("name");

        if (error) throw error;
        setBlocks(data || []);
      } catch (error) {
        console.error("Error fetching blocks:", error);
        toast({
          title: t("common.error"),
          description: t("profile.failedToFetchBlocks"),
          variant: "destructive",
        });
      }
    };

    fetchBlocks();
  }, [userRole, profile?.school_id, toast, t]);

  // Fetch rooms when block is selected (for students)
  useEffect(() => {
    if (userRole !== "student" || !selectedBlock) {
      setRooms([]);
      return;
    }

    const fetchRooms = async () => {
      try {
        const { data, error } = await supabase
          .from("rooms")
          .select("*")
          .eq("block_id", selectedBlock)
          .order("name");

        if (error) throw error;
        setRooms(data || []);
      } catch (error) {
        console.error("Error fetching rooms:", error);
        toast({
          title: t("common.error"),
          description: t("profile.failedToFetchRooms"),
          variant: "destructive",
        });
      }
    };

    fetchRooms();
  }, [selectedBlock, userRole, toast, t]);

  // Always set to editing mode if in setup mode
  useEffect(() => {
    if (isSetupMode) {
      setIsEditing(true);
    }
  }, [isSetupMode]);

  // Update form with existing profile data when available
  useEffect(() => {
    if (!profile) return;

    if (userRole === "student" && profile.role === "student") {
      const studentProfile = profile as Student;
      form.reset({
        name: studentProfile.name || "",
        studentId: studentProfile.studentId || "",
        course: studentProfile.course || "",
        block_id: studentProfile.block_id || "",
        room_id: studentProfile.room_id || "",
        pin: studentProfile.pin || "",
        school: studentProfile.school_id || "",
      });
      setSelectedBlock(studentProfile.block_id || null);
      if (studentProfile.photoUrl) {
        setPhotoPreview(studentProfile.photoUrl);
      }
    } else if (userRole === "teacher" && profile.role === "teacher") {
      const teacherProfile = profile as Teacher;
      form.reset({
        name: teacherProfile.name || "",
        teacherId: teacherProfile.teacherId || "",
        department: teacherProfile.department || "",
        position: teacherProfile.position || "",
        subject: teacherProfile.subject || "",
        school: teacherProfile.school_id || "",
      });
      if (teacherProfile.photoUrl) {
        setPhotoPreview(teacherProfile.photoUrl);
      }
    } else if (userRole === "admin" && profile.role === "admin") {
      const adminProfile = profile as Admin;
      form.reset({
        name: adminProfile.name || "",
        adminId: adminProfile.adminId || "",
        position: adminProfile.position || "",
        school: adminProfile.school_id || "",
      });
      if (adminProfile.photoUrl) {
        setPhotoPreview(adminProfile.photoUrl);
      }
    }
  }, [profile, form, userRole]);

  const handlePhotoClick = () => {
    fileInputRef.current?.click();
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !profile) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: t("profile.fileTooLarge"),
        description: t("profile.selectSmallerImage"),
        variant: "destructive",
      });
      return;
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast({
        title: t("profile.invalidFileType"),
        description: t("profile.selectImageFile"),
        variant: "destructive",
      });
      return;
    }

    try {
      setUploadingPhoto(true);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to Supabase Storage
      const fileExt = file.name.split(".").pop();
      const fileName = `${userRole}-${profile.id}-${Date.now()}.${fileExt}`;
      const filePath = `profile-photos/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from("images")
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from("images")
        .getPublicUrl(filePath);

      // Update profile with new photo URL
      await updateProfile({
        photoUrl: publicUrlData.publicUrl,
      });

      toast({
        title: t("profile.photoUpdated"),
        description: t("profile.photoUpdatedDesc"),
      });
    } catch (error) {
      console.error("Error uploading photo:", error);
      toast({
        title: t("profile.uploadFailed"),
        description: t("profile.failedToUploadPhoto"),
        variant: "destructive",
      });
    } finally {
      setUploadingPhoto(false);
    }
  };

  const onSubmit = async (data: any) => {
    if (!profile) return;

    setSubmitAttempted(true);

    try {
      if (userRole === "student") {
        const studentData = data as StudentFormValues;
        // Get block and room names for display
        const selectedBlockObj = blocks.find(
          (b) => b.id === studentData.block_id
        );
        const selectedRoom = rooms.find((r) => r.id === studentData.room_id);
        // Get selected school name for display
        const schoolId = studentData.school || profile?.school_id;
        const selectedSchool = schools.find((s) => s.id === schoolId);

        await updateProfile({
          name: studentData.name,
          studentId: studentData.studentId,
          course: studentData.course,
          block_id: studentData.block_id,
          room_id: studentData.room_id,
          blockName: selectedBlockObj?.name || "",
          roomNumber: selectedRoom?.name || "",
          pin: studentData.pin,
          school_id: schoolId,
          school: selectedSchool?.name || "", // For backward compatibility - store school name
          schoolName: selectedSchool?.name || "",
        });
      } else if (userRole === "teacher") {
        const teacherData = data as TeacherFormValues;
        const schoolId = teacherData.school || profile?.school_id;
        const selectedSchool = schools.find((s) => s.id === schoolId);

        await updateProfile({
          name: teacherData.name,
          teacherId: teacherData.teacherId,
          department: teacherData.department,
          position: teacherData.position,
          subject: teacherData.subject,
          school_id: schoolId,
          school: selectedSchool?.name || "", // For backward compatibility - store school name
          schoolName: selectedSchool?.name || "",
        });
      } else if (userRole === "admin") {
        const adminData = data as AdminFormValues;
        const schoolId = adminData.school || profile?.school_id;
        const selectedSchool = schools.find((s) => s.id === schoolId);

        await updateProfile({
          name: adminData.name,
          adminId: adminData.adminId,
          position: adminData.position,
          school_id: schoolId,
          school: selectedSchool?.name || "", // For backward compatibility - store school name
        });
      }

      if (!isSetupMode) {
        setIsEditing(false);
      }

      // If in setup mode, show success toast and redirect to dashboard
      if (isSetupMode) {
        toast({
          title: t("profile.setupComplete"),
          description: t("profile.setupCompleteDesc"),
        });
        setTimeout(() => {
          if (userRole === "student") navigate("/student");
          else if (userRole === "teacher") navigate("/teacher");
          else if (userRole === "admin") navigate("/admin");
        }, 500);
      } else {
        toast({
          title: t("common.success"),
          description: t("profile.profileUpdated"),
        });
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: t("common.error"),
        description: t("profile.failedToUpdateProfile"),
        variant: "destructive",
      });
    } finally {
      setSubmitAttempted(false);
    }
  };

  if (!profile || profile.role !== userRole) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold mb-2">
            {t("profile.tabs.profile")}
          </h2>
          <p className="text-gray-600">{t("common.loading")}</p>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-center items-center h-40">
              <p>{t("common.loading")}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if required fields are missing for setup mode
  const isMissingRequiredFields = () => {
    if (!isSetupMode) return false;

    if (userRole === "student") {
      const studentProfile = profile as Student;
      return (
        !studentProfile.course ||
        !studentProfile.block_id ||
        !studentProfile.room_id ||
        !studentProfile.pin ||
        !(studentProfile.school || studentProfile.school_id)
      );
    } else if (userRole === "teacher") {
      const teacherProfile = profile as Teacher;
      return (
        !teacherProfile.department ||
        !teacherProfile.position ||
        !teacherProfile.subject ||
        !(teacherProfile.school || teacherProfile.school_id)
      );
    } else if (userRole === "admin") {
      const adminProfile = profile as Admin;
      return (
        !adminProfile.position || !(adminProfile.school || adminProfile.school_id) || !adminProfile.adminId
      );
    }
    return false;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">{t("profile.tabs.profile")}</h2>
        <p className="text-gray-600">
          {isSetupMode
            ? t("profile.completeProfileMessage")
            : userRole === "student"
            ? t("profile.viewAndManage")
            : userRole === "teacher"
            ? t("teacher.profile.viewAndManageProfile")
            : t("admin.profile.viewAndManageProfile")}
        </p>
      </div>

      {/* Removed duplicate Profile Summary Card to eliminate visual duplication */}

      {/* Missing Fields Alert */}
      {isMissingRequiredFields() && (
        <Alert className="bg-yellow-50 border-yellow-200">
          <AlertCircle className="h-4 w-4 text-amber-800" />
          <AlertDescription className="text-amber-800">
            {t("profile.fillRequiredFields")}
          </AlertDescription>
        </Alert>
      )}

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handlePhotoChange}
        className="hidden"
        accept="image/*"
      />

      {/* Profile Form */}
      <Card>
        <CardHeader>
          <CardTitle>
            {isSetupMode
              ? t("profile.completeYourProfile")
              : isEditing
              ? t("profile.editProfile")
              : t("profile.profileInformation")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {isEditing ? (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                {/* Photo Upload Section */}
                <div className="flex flex-col items-center gap-4 mb-6">
                  <Avatar
                    className="w-20 h-20 sm:w-24 sm:h-24 border-4 border-primary/20 cursor-pointer relative"
                    onClick={handlePhotoClick}
                  >
                    {photoPreview || profile.photoUrl ? (
                      <AvatarImage
                        src={photoPreview || profile.photoUrl}
                        alt={profile.name}
                      />
                    ) : (
                      <AvatarFallback className="bg-secondary/30 flex items-center justify-center">
                        <Camera size={20} className="text-gray-400" />
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex items-center gap-2 w-full sm:w-auto"
                    onClick={handlePhotoClick}
                    disabled={uploadingPhoto}
                  >
                    <Camera size={16} />
                    {uploadingPhoto
                      ? t("profile.uploading")
                      : t("profile.changePhoto")}
                  </Button>
                </div>

                {/* Form Fields */}
                <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2">
                  {/* Name Field - Common to all */}
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("profile.fullName")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("profile.enterFullName")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Role-specific fields */}
                  {userRole === "student" && (
                    <>
                      <FormField
                        control={form.control}
                        name="studentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("profile.studentId")}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t("profile.enterStudentId")}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="course"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("profile.course")}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t("profile.enterCourse")}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* School Display for Students - Show above block selection */}
                      <FormField
                        control={form.control}
                        name="school"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("profile.school")}</FormLabel>
                            <div className="bg-muted p-3 rounded-md">
                              <p className="text-sm font-medium">
                                {(() => {
                                  if (loadingSchools) return "Loading schools...";
                                  const schoolId = field.value || profile?.school_id;
                                  const schoolFromList = schools.find(s => s.id === schoolId);
                                  return schoolFromList?.name || profile?.school || (schoolId ? "Loading school..." : "School not assigned");
                                })()}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Your school assignment cannot be changed here. Contact your administrator if this is incorrect.
                              </p>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="block_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("profile.block")}</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={(value) => {
                                field.onChange(value);
                                setSelectedBlock(value);
                                // Reset room selection when block changes
                                form.setValue("room_id", "");
                              }}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue
                                    placeholder={t("profile.selectBlock")}
                                  />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {blocks.map((block) => (
                                  <SelectItem key={block.id} value={block.id}>
                                    {t("profile.block")} {block.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="room_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("profile.room")}</FormLabel>
                            <Select
                              disabled={!selectedBlock}
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue
                                    placeholder={t("profile.selectRoom")}
                                  />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {rooms.map((room) => (
                                  <SelectItem key={room.id} value={room.id}>
                                    {t("profile.room")} {room.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="pin"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("profile.pin")}</FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder={t("profile.enterPin")}
                                maxLength={6}
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              {t("profile.pinDescription")}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {userRole === "teacher" && (
                    <>
                      <FormField
                        control={form.control}
                        name="teacherId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t("teacher.profile.teacherId")}
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="department"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t("teacher.profile.department")}
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="position"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t("teacher.profile.position")}
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="subject"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t("teacher.profile.subject")}
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {userRole === "admin" && (
                    <>
                      <FormField
                        control={form.control}
                        name="adminId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("admin.profile.adminId")}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t("admin.profile.enterAdminId")}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="position"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("admin.profile.position")}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t("admin.profile.enterPosition")}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {/* School Display - Show user's assigned school (read-only for teachers only, students have it above) */}
                  {userRole === "teacher" && (
                    <FormField
                      control={form.control}
                      name="school"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("profile.school")}</FormLabel>
                          <div className="bg-muted p-3 rounded-md">
                            <p className="text-sm font-medium">
                              {(() => {
                                if (loadingSchools) return "Loading schools...";
                                const schoolId = field.value || profile?.school_id;
                                const schoolFromList = schools.find(s => s.id === schoolId);
                                return schoolFromList?.name || profile?.school || (schoolId ? "Loading school..." : "School not assigned");
                              })()}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              Your school assignment cannot be changed here. Contact your administrator if this is incorrect.
                            </p>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Admin School Field - Manual Entry */}
                  {userRole === "admin" && (
                    <FormField
                      control={form.control}
                      name="school"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("admin.profile.school")}</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t("admin.profile.enterSchool")}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                {/* Form Actions */}
                <div className="flex justify-end gap-2 pt-4">
                  {!isSetupMode && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsEditing(false)}
                    >
                      {t("common.cancel")}
                    </Button>
                  )}
                  <Button
                    type="submit"
                    disabled={submitAttempted || uploadingPhoto}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {submitAttempted
                      ? t("common.saving")
                      : isSetupMode
                      ? t("profile.completeSetup")
                      : t("common.saveChanges")}
                  </Button>
                </div>
              </form>
            </Form>
          ) : (
            // View Mode
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                <Avatar className="w-20 h-20 sm:w-24 sm:h-24 border-2 border-primary/20 flex-shrink-0">
                  <AvatarImage src={profile.photoUrl} />
                  <AvatarFallback className="text-lg">
                    {profile.name?.charAt(0).toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-4 text-center sm:text-left w-full">
                  <div>
                    <h3 className="text-xl font-semibold">{profile.name}</h3>
                    <p className="text-gray-600 break-all">{profile.email}</p>
                  </div>

                  <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2">
                    {userRole === "student" && (
                      <>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("profile.studentId")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Student).studentId ||
                              t("common.notSet")}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("profile.course")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Student).course || t("common.notSet")}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("profile.block")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Student).blockName ||
                              t("common.notSet")}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("profile.room")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Student).roomNumber ||
                              t("common.notSet")}
                          </p>
                        </div>
                      </>
                    )}

                    {userRole === "teacher" && (
                      <>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("teacher.profile.teacherId")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Teacher).teacherId ||
                              t("common.notSet")}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("teacher.profile.department")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Teacher).department ||
                              t("common.notSet")}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("teacher.profile.position")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Teacher).position ||
                              t("common.notSet")}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("teacher.profile.subject")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Teacher).subject || t("common.notSet")}
                          </p>
                        </div>
                      </>
                    )}

                    {userRole === "admin" && (
                      <>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("admin.profile.adminId")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Admin).adminId || t("common.notSet")}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-sm font-medium text-gray-500">
                            {t("admin.profile.position")}
                          </Label>
                          <p className="mt-1 break-all">
                            {(profile as Admin).position || t("common.notSet")}
                          </p>
                        </div>
                      </>
                    )}

                    <div className="space-y-1">
                      <Label className="text-sm font-medium text-gray-500">
                        {t("profile.school")}
                      </Label>
                      <p className="mt-1 break-all">
                        {profile.schoolName ||
                          (userRole !== "admin" &&
                            schools.find((s) => s.id === profile.school)
                              ?.name) ||
                          profile.school ||
                          t("common.notSet")}
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-center sm:justify-start">
                    <Button
                      onClick={() => setIsEditing(true)}
                      className="flex items-center gap-2 w-full sm:w-auto"
                    >
                      <Edit className="w-4 h-4" />
                      {t("profile.editProfile")}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
