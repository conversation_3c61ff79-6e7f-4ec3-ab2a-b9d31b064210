-- Fix for biometric registration validation issue
-- This migration fixes the "Student not found in school" error during admin-supervised biometric registration

-- Update the register_student_in_session function to use more reliable validation
CREATE OR REPLACE FUNCTION register_student_in_session(
  session_id_param UUID,
  student_id_param UUID,
  credential_id_param TEXT,
  public_key_param TEXT,
  device_id_param TEXT
)
RETURNS JSON AS $$
DECLARE
  session_record RECORD;
  result JSON;
BEGIN
  -- Get session details and verify permissions
  SELECT rs.*, p.school_id as admin_school_id INTO session_record
  FROM registration_sessions rs
  JOIN profiles p ON p.user_id = auth.uid()
  WHERE rs.id = session_id_param
  AND p.role = 'admin'
  AND p.school_id = rs.school_id;

  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'error', 'Session not found or unauthorized');
  END IF;

  -- Verify student belongs to the same school and is in the session
  -- First check if student is in the session (they were already validated during session creation)
  IF NOT EXISTS (
    SELECT 1 FROM registration_session_students
    WHERE session_id = session_id_param
    AND student_id = student_id_param
  ) THEN
    RETURN json_build_object('success', false, 'error', 'Student not found in this registration session');
  END IF;

  -- Additional verification: ensure student exists and is a student
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = student_id_param
    AND role = 'student'
  ) THEN
    RETURN json_build_object('success', false, 'error', 'Student profile not found or invalid role');
  END IF;

  -- Check if student already has credentials on this device
  IF EXISTS (
    SELECT 1 FROM biometric_credentials
    WHERE user_id = student_id_param
    AND device_id = device_id_param
  ) THEN
    -- Update session student status to skipped
    UPDATE registration_session_students
    SET registration_status = 'skipped',
        error_message = 'Student already registered on this device'
    WHERE session_id = session_id_param AND student_id = student_id_param;

    RETURN json_build_object('success', false, 'error', 'Student already registered on this device');
  END IF;

  -- Store the biometric credential
  INSERT INTO biometric_credentials (
    user_id, credential_id, public_key, device_id, device_name,
    registered_by, registration_session_id, registration_method
  ) VALUES (
    student_id_param, credential_id_param, public_key_param::bytea,
    device_id_param, session_record.device_name,
    auth.uid(), session_id_param, 'admin_supervised'
  );

  -- Update session student status
  UPDATE registration_session_students
  SET registration_status = 'registered',
      registered_at = NOW()
  WHERE session_id = session_id_param AND student_id = student_id_param;

  -- Update student profile
  UPDATE profiles
  SET biometric_registered = true,
      updated_at = NOW()
  WHERE user_id = student_id_param;

  -- Update session statistics
  UPDATE registration_sessions
  SET registered_students = (
    SELECT COUNT(*) FROM registration_session_students
    WHERE session_id = session_id_param AND registration_status = 'registered'
  ),
  failed_registrations = (
    SELECT COUNT(*) FROM registration_session_students
    WHERE session_id = session_id_param AND registration_status = 'failed'
  ),
  updated_at = NOW()
  WHERE id = session_id_param;

  result := json_build_object(
    'success', true,
    'message', 'Biometric credential registered successfully'
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
