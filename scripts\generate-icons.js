#!/usr/bin/env node

/**
 * Icon Generation Script for iOS Compatibility
 * 
 * This script converts the SVG logo to PNG format in various sizes
 * required for iOS PWA app icons.
 * 
 * Usage: node scripts/generate-icons.js
 * 
 * Requirements:
 * - Install sharp: npm install sharp
 * - Run from project root
 */

const fs = require('fs');
const path = require('path');

// Check if sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.error('❌ Sharp is not installed. Please run: npm install sharp');
  process.exit(1);
}

// Icon sizes needed for iOS and Android
const iconSizes = [
  // iOS App Icons
  { name: 'apple-touch-icon', size: 180 },
  { name: 'apple-touch-icon-152x152', size: 152 },
  { name: 'apple-touch-icon-144x144', size: 144 },
  { name: 'apple-touch-icon-120x120', size: 120 },
  { name: 'apple-touch-icon-114x114', size: 114 },
  { name: 'apple-touch-icon-76x76', size: 76 },
  { name: 'apple-touch-icon-72x72', size: 72 },
  { name: 'apple-touch-icon-60x60', size: 60 },
  { name: 'apple-touch-icon-57x57', size: 57 },
  
  // Android Chrome Icons
  { name: 'android-chrome-192x192', size: 192 },
  { name: 'android-chrome-512x512', size: 512 },
  
  // Standard Favicons
  { name: 'favicon-32x32', size: 32 },
  { name: 'favicon-16x16', size: 16 },
];

async function generateIcons() {
  const svgPath = path.join(__dirname, '../public/logo.svg');
  const publicDir = path.join(__dirname, '../public');
  
  // Check if SVG exists
  if (!fs.existsSync(svgPath)) {
    console.error('❌ SVG logo not found at:', svgPath);
    process.exit(1);
  }
  
  console.log('🎨 Generating PNG icons from SVG...');
  
  try {
    // Read SVG content
    const svgBuffer = fs.readFileSync(svgPath);
    
    // Generate each icon size
    for (const icon of iconSizes) {
      const outputPath = path.join(publicDir, `${icon.name}.png`);
      
      await sharp(svgBuffer)
        .resize(icon.size, icon.size, {
          fit: 'contain',
          background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background
        })
        .png({
          quality: 100,
          compressionLevel: 0 // No compression for best quality
        })
        .toFile(outputPath);
      
      console.log(`✅ Generated: ${icon.name}.png (${icon.size}x${icon.size})`);
    }
    
    console.log('\n🎉 All icons generated successfully!');
    console.log('\n📱 iOS PWA icons are now ready. The app icon should appear correctly on iPhones.');
    
  } catch (error) {
    console.error('❌ Error generating icons:', error);
    process.exit(1);
  }
}

// Run the script
generateIcons();
