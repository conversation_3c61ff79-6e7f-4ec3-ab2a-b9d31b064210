-- Enhanced Database Schema for Admin-Supervised Bulk Registration
-- This migration adds support for device tracking, bulk registration sessions, and improved credential management

-- Add device tracking to biometric credentials
ALTER TABLE biometric_credentials 
ADD COLUMN IF NOT EXISTS device_id TEXT,
ADD COLUMN IF NOT EXISTS device_name TEXT,
ADD COLUMN IF NOT EXISTS registered_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS registration_session_id UUID,
ADD COLUMN IF NOT EXISTS registration_method TEXT DEFAULT 'individual' CHECK (registration_method IN ('individual', 'admin_supervised', 'bulk'));

-- Create registration sessions table
CREATE TABLE IF NOT EXISTS registration_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id TEXT NOT NULL,
  device_name TEXT,
  admin_id UUID NOT NULL REFERENCES auth.users(id),
  school_id UUID NOT NULL REFERENCES schools(id),
  session_name TEXT,
  total_students INTEGER DEFAULT 0,
  registered_students INTEGER DEFAULT 0,
  failed_registrations INTEGER DEFAULT 0,
  session_status TEXT DEFAULT 'active' CHECK (session_status IN ('active', 'completed', 'cancelled')),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create registration session students table (tracks individual student registrations within a session)
CREATE TABLE IF NOT EXISTS registration_session_students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES registration_sessions(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES auth.users(id),
  student_email TEXT NOT NULL,
  student_name TEXT NOT NULL,
  registration_status TEXT DEFAULT 'pending' CHECK (registration_status IN ('pending', 'registered', 'failed', 'skipped')),
  registration_order INTEGER,
  registered_at TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create device registry table
CREATE TABLE IF NOT EXISTS device_registry (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id TEXT UNIQUE NOT NULL,
  device_name TEXT NOT NULL,
  device_type TEXT DEFAULT 'kiosk' CHECK (device_type IN ('kiosk', 'tablet', 'mobile')),
  school_id UUID NOT NULL REFERENCES schools(id),
  room_id UUID REFERENCES rooms(id),
  is_active BOOLEAN DEFAULT true,
  last_seen TIMESTAMPTZ DEFAULT NOW(),
  registered_students_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_device_id ON biometric_credentials(device_id);
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_registered_by ON biometric_credentials(registered_by);
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_session_id ON biometric_credentials(registration_session_id);
CREATE INDEX IF NOT EXISTS idx_registration_sessions_admin_id ON registration_sessions(admin_id);
CREATE INDEX IF NOT EXISTS idx_registration_sessions_school_id ON registration_sessions(school_id);
CREATE INDEX IF NOT EXISTS idx_registration_sessions_device_id ON registration_sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_registration_session_students_session_id ON registration_session_students(session_id);
CREATE INDEX IF NOT EXISTS idx_registration_session_students_student_id ON registration_session_students(student_id);
CREATE INDEX IF NOT EXISTS idx_device_registry_school_id ON device_registry(school_id);
CREATE INDEX IF NOT EXISTS idx_device_registry_device_id ON device_registry(device_id);

-- Add RLS policies
ALTER TABLE registration_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE registration_session_students ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_registry ENABLE ROW LEVEL SECURITY;

-- Registration sessions policies
CREATE POLICY "Admins can manage registration sessions for their school" ON registration_sessions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.user_id = auth.uid() 
      AND p.role = 'admin' 
      AND p.school_id = registration_sessions.school_id
    )
  );

-- Registration session students policies
CREATE POLICY "Admins can manage session students for their school" ON registration_session_students
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM registration_sessions rs
      JOIN profiles p ON p.user_id = auth.uid()
      WHERE rs.id = registration_session_students.session_id
      AND p.role = 'admin'
      AND p.school_id = rs.school_id
    )
  );

-- Device registry policies
CREATE POLICY "Admins can manage devices for their school" ON device_registry
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.user_id = auth.uid() 
      AND p.role = 'admin' 
      AND p.school_id = device_registry.school_id
    )
  );

-- Update trigger for registration_sessions
CREATE OR REPLACE FUNCTION update_registration_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER registration_sessions_updated_at
  BEFORE UPDATE ON registration_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_registration_sessions_updated_at();

-- Update trigger for device_registry
CREATE OR REPLACE FUNCTION update_device_registry_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER device_registry_updated_at
  BEFORE UPDATE ON device_registry
  FOR EACH ROW
  EXECUTE FUNCTION update_device_registry_updated_at();

-- Function to automatically update device last_seen
CREATE OR REPLACE FUNCTION update_device_last_seen(device_id_param TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE device_registry 
  SET last_seen = NOW() 
  WHERE device_id = device_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update registered students count on device
CREATE OR REPLACE FUNCTION update_device_student_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Increment count when new credential is added
    UPDATE device_registry 
    SET registered_students_count = registered_students_count + 1
    WHERE device_id = NEW.device_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement count when credential is removed
    UPDATE device_registry 
    SET registered_students_count = GREATEST(0, registered_students_count - 1)
    WHERE device_id = OLD.device_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update device student count
CREATE TRIGGER biometric_credentials_device_count_trigger
  AFTER INSERT OR DELETE ON biometric_credentials
  FOR EACH ROW
  EXECUTE FUNCTION update_device_student_count();

-- RPC Functions for Bulk Registration

-- Function to start a new registration session
CREATE OR REPLACE FUNCTION start_registration_session(
  device_id_param TEXT,
  device_name_param TEXT,
  session_name_param TEXT,
  school_id_param UUID,
  student_emails TEXT[]
)
RETURNS JSON AS $$
DECLARE
  session_id UUID;
  student_record RECORD;
  student_count INTEGER := 0;
  result JSON;
BEGIN
  -- Verify admin permissions
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
    AND school_id = school_id_param
  ) THEN
    RETURN json_build_object('success', false, 'error', 'Unauthorized: Admin access required');
  END IF;

  -- Register or update device
  INSERT INTO device_registry (device_id, device_name, school_id, created_by)
  VALUES (device_id_param, device_name_param, school_id_param, auth.uid())
  ON CONFLICT (device_id)
  DO UPDATE SET
    device_name = device_name_param,
    last_seen = NOW(),
    is_active = true;

  -- Create registration session
  INSERT INTO registration_sessions (
    device_id, device_name, admin_id, school_id, session_name, total_students
  ) VALUES (
    device_id_param, device_name_param, auth.uid(), school_id_param,
    session_name_param, array_length(student_emails, 1)
  ) RETURNING id INTO session_id;

  -- Add students to session
  FOR i IN 1..array_length(student_emails, 1) LOOP
    SELECT p.user_id, p.email, p.name INTO student_record
    FROM profiles p
    WHERE p.email = student_emails[i]
    AND p.school_id = school_id_param
    AND p.role = 'student';

    IF FOUND THEN
      INSERT INTO registration_session_students (
        session_id, student_id, student_email, student_name, registration_order
      ) VALUES (
        session_id, student_record.user_id, student_record.email,
        student_record.name, i
      );
      student_count := student_count + 1;
    END IF;
  END LOOP;

  -- Update actual student count
  UPDATE registration_sessions
  SET total_students = student_count
  WHERE id = session_id;

  result := json_build_object(
    'success', true,
    'session_id', session_id,
    'total_students', student_count,
    'device_id', device_id_param
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to register biometric for student in bulk session
CREATE OR REPLACE FUNCTION register_student_in_session(
  session_id_param UUID,
  student_id_param UUID,
  credential_id_param TEXT,
  public_key_param TEXT,
  device_id_param TEXT
)
RETURNS JSON AS $$
DECLARE
  session_record RECORD;
  result JSON;
BEGIN
  -- Get session details and verify permissions
  SELECT rs.*, p.school_id as admin_school_id INTO session_record
  FROM registration_sessions rs
  JOIN profiles p ON p.user_id = auth.uid()
  WHERE rs.id = session_id_param
  AND p.role = 'admin'
  AND p.school_id = rs.school_id;

  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'error', 'Session not found or unauthorized');
  END IF;

  -- Verify student belongs to the same school and is in the session
  -- First check if student is in the session (they were already validated during session creation)
  IF NOT EXISTS (
    SELECT 1 FROM registration_session_students
    WHERE session_id = session_id_param
    AND student_id = student_id_param
  ) THEN
    RETURN json_build_object('success', false, 'error', 'Student not found in this registration session');
  END IF;

  -- Additional verification: ensure student exists and is a student
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = student_id_param
    AND role = 'student'
  ) THEN
    RETURN json_build_object('success', false, 'error', 'Student profile not found or invalid role');
  END IF;

  -- Check if student already has credentials on this device
  IF EXISTS (
    SELECT 1 FROM biometric_credentials
    WHERE user_id = student_id_param
    AND device_id = device_id_param
  ) THEN
    -- Update session student status to skipped
    UPDATE registration_session_students
    SET registration_status = 'skipped',
        error_message = 'Student already registered on this device'
    WHERE session_id = session_id_param AND student_id = student_id_param;

    RETURN json_build_object('success', false, 'error', 'Student already registered on this device');
  END IF;

  -- Insert biometric credential
  INSERT INTO biometric_credentials (
    user_id, credential_id, public_key, counter, device_id, device_name,
    registered_by, registration_session_id, registration_method
  ) VALUES (
    student_id_param, credential_id_param, public_key_param, 0,
    device_id_param, session_record.device_name, auth.uid(),
    session_id_param, 'admin_supervised'
  );

  -- Update student profile
  UPDATE profiles
  SET biometric_registered = true
  WHERE user_id = student_id_param;

  -- Update session student status
  UPDATE registration_session_students
  SET registration_status = 'registered',
      registered_at = NOW()
  WHERE session_id = session_id_param AND student_id = student_id_param;

  -- Update session progress
  UPDATE registration_sessions
  SET registered_students = registered_students + 1,
      updated_at = NOW()
  WHERE id = session_id_param;

  result := json_build_object(
    'success', true,
    'student_id', student_id_param,
    'session_id', session_id_param
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get student by biometric (for biometric-first authentication)
CREATE OR REPLACE FUNCTION get_student_by_biometric(
  credential_id_param TEXT,
  device_id_param TEXT
)
RETURNS JSON AS $$
DECLARE
  student_record RECORD;
  result JSON;
BEGIN
  -- Find student by credential and device
  SELECT p.user_id, p.email, p.name, p.school_id, bc.device_id
  INTO student_record
  FROM biometric_credentials bc
  JOIN profiles p ON p.user_id = bc.user_id
  WHERE bc.credential_id = credential_id_param
  AND bc.device_id = device_id_param
  AND p.role = 'student';

  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'error', 'Student not found for this biometric');
  END IF;

  result := json_build_object(
    'success', true,
    'student_id', student_record.user_id,
    'email', student_record.email,
    'name', student_record.name,
    'school_id', student_record.school_id
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get registration session details
CREATE OR REPLACE FUNCTION get_registration_session(session_id_param UUID)
RETURNS JSON AS $$
DECLARE
  session_record RECORD;
  students_data JSON;
  result JSON;
BEGIN
  -- Verify admin permissions and get session
  SELECT rs.* INTO session_record
  FROM registration_sessions rs
  JOIN profiles p ON p.user_id = auth.uid()
  WHERE rs.id = session_id_param
  AND p.role = 'admin'
  AND p.school_id = rs.school_id;

  IF NOT FOUND THEN
    RETURN json_build_object('success', false, 'error', 'Session not found or unauthorized');
  END IF;

  -- Get students in session
  SELECT json_agg(
    json_build_object(
      'student_id', student_id,
      'student_email', student_email,
      'student_name', student_name,
      'registration_status', registration_status,
      'registration_order', registration_order,
      'registered_at', registered_at,
      'error_message', error_message
    ) ORDER BY registration_order
  ) INTO students_data
  FROM registration_session_students
  WHERE session_id = session_id_param;

  result := json_build_object(
    'success', true,
    'session', json_build_object(
      'id', session_record.id,
      'device_id', session_record.device_id,
      'device_name', session_record.device_name,
      'session_name', session_record.session_name,
      'total_students', session_record.total_students,
      'registered_students', session_record.registered_students,
      'failed_registrations', session_record.failed_registrations,
      'session_status', session_record.session_status,
      'started_at', session_record.started_at,
      'completed_at', session_record.completed_at
    ),
    'students', COALESCE(students_data, '[]'::json)
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
