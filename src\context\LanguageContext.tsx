import React, { createContext, useContext, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { useAuth } from "./AuthContext";
import { toast } from "@/components/ui/safe-toast";

type LanguageCode = "en" | "tr";

interface LanguageContextType {
  currentLanguage: LanguageCode;
  changeLanguage: (language: LanguageCode) => Promise<void>;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { i18n } = useTranslation();
  const { profile } = useAuth();
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>("en");
  const [isLoading, setIsLoading] = useState(false);

  // Initialize language from profile or localStorage
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Initializing language

        // Get the language from profile, localStorage, or default to English
        let targetLanguage: LanguageCode = "en";

        // If user is logged in, use their preferred language from profile
        if (profile?.preferred_language) {
          targetLanguage = profile.preferred_language as LanguageCode;
        } else {
          // Otherwise use the language from localStorage or browser
          const detectedLang =
            localStorage.getItem("i18nextLng") || i18n.language;
          targetLanguage = detectedLang === "tr" ? "tr" : "en";
        }

        // Update state
        setCurrentLanguage(targetLanguage);

        // Store in localStorage
        localStorage.setItem("i18nextLng", targetLanguage);

        // Change language in i18next
        await i18n.changeLanguage(targetLanguage);

        // Force reload of translations
        try {
          await i18n.reloadResources([targetLanguage], ["translation"]);
        } catch (reloadError) {
          console.error("Error reloading resources:", reloadError);
        }
      } catch (error) {
        console.error("Error initializing language:", error);
        // Fallback to English
        setCurrentLanguage("en");
        localStorage.setItem("i18nextLng", "en");
        await i18n.changeLanguage("en");
      }
    };

    initializeLanguage();
  }, [profile, i18n]);

  // Change language function
  const changeLanguage = async (language: LanguageCode) => {
    setIsLoading(true);
    try {
      // Store in localStorage first
      localStorage.setItem("i18nextLng", language);

      // Update state
      setCurrentLanguage(language);

      // Change language in i18next and wait for it to complete
      await i18n.changeLanguage(language);

      // Force reload of translations
      try {
        await i18n.reloadResources([language], ["translation"]);
      } catch (reloadError) {
        console.error("Error reloading resources:", reloadError);
      }

      // If user is logged in, update their profile
      if (profile?.id) {
        const { error } = await supabase
          .from("profiles")
          .update({ preferred_language: language })
          .eq("id", profile.id);

        if (error) {
          console.error("Error updating language preference:", error);

          // Use the i18n instance directly to ensure we get the updated translations
          const errorTitle = i18n.t("common.error", { lng: language });
          const errorMessage = i18n.t("settings.languageUpdateError", { lng: language }) ||
            "Failed to update language preference";
          toast.error(errorTitle, { description: errorMessage });
        } else {
          // Use the i18n instance directly to ensure we get the updated translations
          const successTitle = i18n.t("common.success", { lng: language });
          const successMessage = i18n.t("settings.languageChanged", { lng: language }) ||
            "Language changed successfully";
          toast.success(successTitle, { description: successMessage });
        }
      }
    } catch (error) {
      console.error("Error changing language:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LanguageContext.Provider
      value={{ currentLanguage, changeLanguage, isLoading }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};
