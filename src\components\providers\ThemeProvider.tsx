import { createContext, useContext, useEffect, useState } from "react";

type Theme = "light" | "dark" | "system";

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

interface ThemeProviderState {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

const initialState: ThemeProviderState = {
  theme: "light",
  setTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "light",
  storageKey = "theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  );

  useEffect(() => {
    const root = window.document.documentElement;

    root.classList.remove("light", "dark");

    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light";

      root.classList.add(systemTheme);

      // Force a CSS refresh by adding and removing a temporary class
      setTimeout(() => {
        root.classList.add("theme-refreshing");
        setTimeout(() => {
          root.classList.remove("theme-refreshing");
        }, 10);
      }, 0);

      return;
    }

    root.classList.add(theme);

    // Force a CSS refresh by adding and removing a temporary class
    setTimeout(() => {
      root.classList.add("theme-refreshing");
      setTimeout(() => {
        root.classList.remove("theme-refreshing");
      }, 10);
    }, 0);
  }, [theme]);

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      localStorage.setItem(storageKey, theme);

      // Force a CSS refresh by adding a style tag and removing it
      const styleTag = document.createElement("style");
      styleTag.textContent =
        theme === "dark" ? ".dark { --primary: 32 89% 56% !important; }" : "";
      document.head.appendChild(styleTag);
      setTimeout(() => {
        document.head.removeChild(styleTag);
      }, 100);

      setTheme(theme);
    },
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};
