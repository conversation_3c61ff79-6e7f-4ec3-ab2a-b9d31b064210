import { useState, useEffect } from "react";
import { useExcuses } from "@/hooks/useExcuses";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { format } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import { calculateDaysBetween, formatDuration } from "@/lib/date-utils";
import { useToast } from "@/hooks/use-toast";
import { Excuse } from "@/lib/types";
import { useTranslation } from "react-i18next";
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Calendar,
  Search,
  Filter,
  User,
  DoorClosed,
  X,
  Check,
  Download,
  BarChart,
  Trash2,
  ChevronDown,
  FileSpreadsheet,
  FileImage,
  Globe,
  Settings,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { BRANDING, getBranding } from "@/config/branding";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import ExcuseSettings from "./ExcuseSettings";
import ExcuseCleanupSettings from "./ExcuseCleanupSettings";

export default function AdminExcusesManagement() {
  const { profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Create the getStatusBadge function with translation
  const getStatusBadge = createGetStatusBadge(t);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedExcuse, setSelectedExcuse] = useState<Excuse | null>(null);
  const [teacherNotes, setTeacherNotes] = useState("");
  const [teachers, setTeachers] = useState<{ id: string; name: string }[]>([]);
  const [selectedTeacher, setSelectedTeacher] = useState<string>("all");
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
  });

  // Fetch excuses using our custom hook
  const {
    excuses,
    loading,
    isUpdating,
    error,
    fetchExcuses,
    updateExcuseStatus,
    deleteExcuse,
  } = useExcuses({
    role: "admin",
    status: statusFilter !== "all" ? (statusFilter as any) : undefined,
  });

  // Fetch teachers from the current school only
  useEffect(() => {
    const fetchTeachers = async () => {
      if (!profile?.school_id) return;

      try {
        let query = supabase
          .from("profiles")
          .select("id, name")
          .eq("role", "teacher");

        // Filter by school_id for non-system admins
        if (profile.accessLevel !== 3) {
          query = query.eq("school_id", profile.school_id);
        }

        const { data, error } = await query;

        if (error) throw error;
        setTeachers(data || []);
      } catch (err) {
        console.error("Error fetching teachers:", err);
      }
    };

    fetchTeachers();
  }, [profile?.school_id, profile?.accessLevel]);

  // Calculate statistics
  useEffect(() => {
    if (!loading && excuses.length > 0) {
      const total = excuses.length;
      const pending = excuses.filter((e) => e.status === "pending").length;
      const approved = excuses.filter((e) => e.status === "approved").length;
      const rejected = excuses.filter((e) => e.status === "rejected").length;

      setStats({
        total,
        pending,
        approved,
        rejected,
      });
    }
  }, [excuses, loading]);

  // Filter excuses based on search query and teacher filter
  const filteredExcuses = excuses.filter((excuse) => {
    // Apply teacher filter
    if (selectedTeacher !== "all" && excuse.teacher_id !== selectedTeacher) {
      return false;
    }

    // Apply search filter
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      excuse.studentName?.toLowerCase().includes(searchLower) ||
      excuse.roomName?.toLowerCase().includes(searchLower) ||
      excuse.reason.toLowerCase().includes(searchLower)
    );
  });

  // Handle excuse approval
  const handleApproveExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "approved", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error approving excuse:", error);
    }
  };

  // Handle excuse rejection
  const handleRejectExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "rejected", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error rejecting excuse:", error);
    }
  };

  // Handle excuse deletion
  const handleDeleteExcuse = async (excuseId: string) => {
    try {
      await deleteExcuse(excuseId);
      setConfirmDelete(null);
      toast({
        title: t("admin.excusesManagement.excuseDeleted"),
        description: t("admin.excusesManagement.excuseDeletedSuccess"),
      });
    } catch (error) {
      console.error("Error deleting excuse:", error);
      toast({
        title: t("admin.excusesManagement.error"),
        description: t("admin.excusesManagement.failedToDelete"),
        variant: "destructive",
      });
    }
  };

  // Export excuses to CSV
  const exportToCSV = () => {
    const headers = [
      t("admin.excusesManagement.dateRange"),
      t("admin.excusesManagement.student"),
      t("admin.excusesManagement.room"),
      t("admin.excusesManagement.reason"),
      t("admin.excusesManagement.time"),
      t("admin.excusesManagement.status"),
      t("admin.excusesManagement.reviewedBy"),
    ];
    const rows = filteredExcuses.map((excuse) => [
      excuse.start_date === excuse.end_date
        ? format(new Date(excuse.start_date), "dd/MM/yyyy")
        : `${format(new Date(excuse.start_date), "dd/MM/yyyy")} - ${format(new Date(excuse.end_date), "dd/MM/yyyy")}`,
      excuse.studentName || t("admin.excusesManagement.unknownStudent"),
      excuse.roomName || t("admin.excusesManagement.unknownRoom"),
      excuse.reason,
      excuse.start_time && excuse.end_time
        ? `${excuse.start_time} - ${excuse.end_time}`
        : t("admin.excusesManagement.notSpecified"),
      t(`admin.excusesManagement.status.${excuse.status}`),
      excuse.teacherName || t("admin.excusesManagement.notApplicable"),
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map((row) =>
        row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(",")
      ),
    ].join("\n");

    downloadFile(csvContent, `${t("admin.excusesManagement.excusesReport")}_${format(new Date(), "yyyy-MM-dd")}.csv`, "text/csv");
    showExportSuccess("CSV");
  };

  // Export excuses to PDF - Mobile-friendly version
  const exportToPDF = async () => {
    try {
      // Show loading toast
      toast({
        title: t("admin.excusesManagement.generatingPDF"),
        description: t("admin.excusesManagement.pleaseWait"),
        duration: 10000,
      });

      // Create a temporary container for the PDF content
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '0';
      tempContainer.style.width = '794px'; // A4 width in pixels
      tempContainer.style.backgroundColor = 'white';
      tempContainer.style.padding = '40px';
      tempContainer.style.fontFamily = 'Arial, sans-serif';
      tempContainer.style.fontSize = '14px';
      tempContainer.style.lineHeight = '1.5';
      tempContainer.style.color = '#333333';

      // Generate the PDF content HTML
      const pdfContent = generatePDFContent();
      tempContainer.innerHTML = pdfContent;

      // Add to DOM temporarily
      document.body.appendChild(tempContainer);

      // Wait a moment for content to render
      await new Promise(resolve => setTimeout(resolve, 100));

      // Convert to canvas
      const canvas = await html2canvas(tempContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794,
        height: Math.max(1123, tempContainer.scrollHeight + 100),
        logging: false,
      });

      // Remove temporary container
      document.body.removeChild(tempContainer);

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      const imgData = canvas.toDataURL('image/png', 1.0);

      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Generate filename
      const filename = `${t("admin.excusesManagement.excusesReport")}_${format(new Date(), "yyyy-MM-dd")}.pdf`;

      // Save the PDF
      pdf.save(filename);

      // Show success
      showExportSuccess("PDF");

    } catch (error) {
      console.error("PDF generation error:", error);

      // Fallback to browser print
      toast({
        title: t("admin.excusesManagement.pdfGenerationError"),
        description: t("admin.excusesManagement.tryingBrowserPrint"),
        variant: "destructive",
      });

      setTimeout(() => {
        const htmlContent = generateHTMLContent(true);
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          printWindow.document.write(htmlContent);
          printWindow.document.close();
          printWindow.onload = () => {
            setTimeout(() => {
              printWindow.print();
              printWindow.close();
            }, 500);
          };
        }
      }, 1000);
    }
  };

  // Export excuses to HTML
  const exportToHTML = () => {
    const htmlContent = generateHTMLContent(false);
    downloadFile(htmlContent, `${t("admin.excusesManagement.excusesReport")}_${format(new Date(), "yyyy-MM-dd")}.html`, "text/html");
    showExportSuccess("HTML");
  };

  // Generate beautiful HTML content
  const generateHTMLContent = (forPrint = false) => {
    const locale = t("common.locale") === "tr" ? tr : enUS;
    const currentDate = format(new Date(), "d MMMM yyyy", { locale });
    const totalExcuses = filteredExcuses.length;
    const pendingCount = filteredExcuses.filter(e => e.status === "pending").length;
    const approvedCount = filteredExcuses.filter(e => e.status === "approved").length;
    const rejectedCount = filteredExcuses.filter(e => e.status === "rejected").length;

    return `
<!DOCTYPE html>
<html lang="${t("common.locale") === "tr" ? "tr" : "en"}" dir="${t("common.direction") === "rtl" ? "rtl" : "ltr"}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t("admin.excusesManagement.excusesReport")} - ${currentDate}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.2;
            color: #1a202c;
            background: ${forPrint ? '#fff' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
            min-height: 100vh;
            padding: ${forPrint ? '0' : '10px'};
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: ${forPrint ? '0' : '20px'};
            box-shadow: ${forPrint ? 'none' : '0 25px 50px rgba(0,0,0,0.15)'};
            overflow: hidden;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: ${forPrint ? '8px 20px' : '16px 30px'};
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: ${forPrint ? 'none' : 'float 20s ease-in-out infinite'};
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: ${forPrint ? '1.4rem' : '1.8rem'};
            font-weight: 800;
            margin-bottom: ${forPrint ? '2px' : '4px'};
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            letter-spacing: -0.5px;
        }

        .header p {
            font-size: ${forPrint ? '0.75rem' : '0.9rem'};
            opacity: 0.95;
            margin-bottom: ${forPrint ? '1px' : '3px'};
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: ${forPrint ? '6px' : '12px'};
            padding: ${forPrint ? '8px 15px' : '16px 25px'};
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
        }

        .stat-card {
            background: white;
            padding: ${forPrint ? '8px 12px' : '14px 18px'};
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: none;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-color);
        }

        .stat-card:hover {
            transform: ${forPrint ? 'none' : 'translateY(-3px)'};
        }

        .stat-card.total { --accent-color: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); }
        .stat-card.pending { --accent-color: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); }
        .stat-card.approved { --accent-color: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .stat-card.rejected { --accent-color: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }

        .stat-number {
            font-size: ${forPrint ? '1.3rem' : '1.8rem'};
            font-weight: 800;
            margin-bottom: ${forPrint ? '1px' : '3px'};
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-size: ${forPrint ? '0.65rem' : '0.75rem'};
        }

        .content {
            padding: ${forPrint ? '12px 15px' : '24px 30px'};
        }

        .table-container {
            overflow-x: auto;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border: 1px solid #e2e8f0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: ${forPrint ? '8px 6px' : '14px 12px'};
            text-align: left;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-size: ${forPrint ? '0.7rem' : '0.8rem'};
            position: relative;
        }

        th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
        }

        td {
            padding: ${forPrint ? '8px 6px' : '12px 12px'};
            border-bottom: 1px solid #f1f5f9;
            vertical-align: top;
            font-size: ${forPrint ? '0.8rem' : '0.9rem'};
            font-weight: 500;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: ${forPrint ? 'inherit' : 'linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%)'};
            transform: ${forPrint ? 'none' : 'scale(1.01)'};
            transition: all 0.2s ease;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: ${forPrint ? '4px 8px' : '6px 12px'};
            border-radius: 25px;
            font-size: ${forPrint ? '0.7rem' : '0.8rem'};
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .status-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: ${forPrint ? 'none' : 'shimmer 2s infinite'};
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .status-pending {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .status-approved {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status-rejected {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .reason-cell {
            max-width: 300px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .footer {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: ${forPrint ? '8px 15px' : '16px 25px'};
            text-align: center;
            color: #64748b;
            border-top: 2px solid #e2e8f0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .footer p {
            margin-bottom: ${forPrint ? '2px' : '4px'};
            font-size: ${forPrint ? '0.75rem' : '0.85rem'};
            font-weight: 500;
        }

        .export-info {
            font-size: 0.9rem;
            font-style: italic;
        }

        @media print {
            body { background: white !important; padding: 0 !important; }
            .container { box-shadow: none !important; border-radius: 0 !important; }
            .stat-card:hover { transform: none !important; }
            tr:hover { background: inherit !important; }
        }

        @media (max-width: 768px) {
            .header {
                padding: 12px 20px;
            }
            .header h1 {
                font-size: 1.5rem;
                margin-bottom: 3px;
            }
            .header p {
                font-size: 0.8rem;
                margin-bottom: 3px;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                padding: 15px;
            }
            .stat-card {
                padding: 12px 15px;
            }
            .stat-number {
                font-size: 1.5rem;
            }
            .stat-label {
                font-size: 0.75rem;
            }
            .content { padding: 20px; }
            th, td { padding: 12px 8px; font-size: 0.9rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>📋 ${t("admin.excusesManagement.excusesReport")}</h1>
                <p>${t("admin.excusesManagement.comprehensiveReport")}</p>
                <p><strong>${t("admin.excusesManagement.generatedOn")}:</strong> ${currentDate}</p>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-number">${totalExcuses}</div>
                <div class="stat-label">${t("admin.excusesManagement.totalExcuses")}</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number">${pendingCount}</div>
                <div class="stat-label">${t("admin.excusesManagement.pending")}</div>
            </div>
            <div class="stat-card approved">
                <div class="stat-number">${approvedCount}</div>
                <div class="stat-label">${t("admin.excusesManagement.approved")}</div>
            </div>
            <div class="stat-card rejected">
                <div class="stat-number">${rejectedCount}</div>
                <div class="stat-label">${t("admin.excusesManagement.rejected")}</div>
            </div>
        </div>

        <div class="content">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>📅 ${t("admin.excusesManagement.dateRange")}</th>
                            <th>👤 ${t("admin.excusesManagement.student")}</th>
                            <th>🏫 ${t("admin.excusesManagement.room")}</th>
                            <th>📝 ${t("admin.excusesManagement.reason")}</th>
                            <th>⏰ ${t("admin.excusesManagement.time")}</th>
                            <th>📊 ${t("admin.excusesManagement.status")}</th>
                            <th>👨‍🏫 ${t("admin.excusesManagement.reviewedBy")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredExcuses.map(excuse => `
                            <tr>
                                <td>
                                    <strong>${excuse.start_date === excuse.end_date
                                        ? format(new Date(excuse.start_date), "d MMM yyyy", { locale })
                                        : `${format(new Date(excuse.start_date), "d MMM", { locale })} - ${format(new Date(excuse.end_date), "d MMM yyyy", { locale })}`
                                    }</strong>
                                </td>
                                <td><strong>${excuse.studentName || t("admin.excusesManagement.unknownStudent")}</strong></td>
                                <td>${excuse.roomName || t("admin.excusesManagement.unknownRoom")}</td>
                                <td class="reason-cell">${excuse.reason}</td>
                                <td>${excuse.start_time && excuse.end_time ? `${excuse.start_time} - ${excuse.end_time}` : t("admin.excusesManagement.notSpecified")}</td>
                                <td>
                                    <span class="status-badge status-${excuse.status}">
                                        ${excuse.status === 'pending' ? t("admin.excusesManagement.pending") :
                                          excuse.status === 'approved' ? t("admin.excusesManagement.approved") :
                                          excuse.status === 'rejected' ? t("admin.excusesManagement.rejected") : excuse.status}
                                    </span>
                                </td>
                                <td>${excuse.teacherName || t("admin.excusesManagement.notApplicable")}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p><strong>${t("app.name")}</strong></p>
            <p class="export-info">${t("admin.excusesManagement.reportContains", { count: totalExcuses, date: currentDate })}</p>
        </div>
    </div>
</body>
</html>`;
  };

  // Helper function to download files
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: `${mimeType};charset=utf-8;` });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Helper function to show export success message
  const showExportSuccess = (format: string) => {
    toast({
      title: t("admin.excusesManagement.exportComplete"),
      description: t("admin.excusesManagement.exportSuccess", { count: filteredExcuses.length, format }),
    });
  };

  // Generate clean PDF content for mobile-friendly PDF generation
  const generatePDFContent = () => {
    const locale = t("common.locale") === "tr" ? tr : enUS;
    const currentDate = format(new Date(), "d MMMM yyyy", { locale });
    const totalExcuses = filteredExcuses.length;
    const pendingCount = filteredExcuses.filter(e => e.status === "pending").length;
    const approvedCount = filteredExcuses.filter(e => e.status === "approved").length;
    const rejectedCount = filteredExcuses.filter(e => e.status === "rejected").length;

    // Get internationalized branding
    const currentBranding = getBranding(i18n.language);

    return `
      <div style="font-family: Arial, sans-serif; color: #333; line-height: 1.4;">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #4f46e5; padding-bottom: 20px;">
          <h1 style="color: #4f46e5; margin: 0; font-size: 24px; font-weight: bold;">
            ${currentBranding.APP_NAME}
          </h1>
          <h2 style="color: #666; margin: 10px 0 0 0; font-size: 18px;">
            ${t("admin.excusesManagement.excusesReport")}
          </h2>
          <p style="color: #888; margin: 5px 0 0 0; font-size: 14px;">
            ${currentDate}
          </p>
        </div>

        <!-- Summary -->
        <div style="margin-bottom: 25px; padding: 20px; background-color: #f8fafc; border-radius: 8px; border-left: 4px solid #4f46e5;">
          <h3 style="margin: 0 0 15px 0; color: #4f46e5; font-size: 16px;">
            ${t("admin.excusesManagement.summary")}
          </h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; font-size: 14px;">
            <div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px; border: 1px solid #e5e7eb;">
              <div style="font-size: 18px; font-weight: bold; color: #374151;">${totalExcuses}</div>
              <div style="color: #6b7280; font-size: 12px;">${t("admin.excusesManagement.totalExcuses")}</div>
            </div>
            <div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px; border: 1px solid #e5e7eb;">
              <div style="font-size: 18px; font-weight: bold; color: #f59e0b;">${pendingCount}</div>
              <div style="color: #6b7280; font-size: 12px;">${t("admin.excusesManagement.pending")}</div>
            </div>
            <div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px; border: 1px solid #e5e7eb;">
              <div style="font-size: 18px; font-weight: bold; color: #10b981;">${approvedCount}</div>
              <div style="color: #6b7280; font-size: 12px;">${t("admin.excusesManagement.approved")}</div>
            </div>
            <div style="text-align: center; padding: 10px; background-color: white; border-radius: 6px; border: 1px solid #e5e7eb;">
              <div style="font-size: 18px; font-weight: bold; color: #ef4444;">${rejectedCount}</div>
              <div style="color: #6b7280; font-size: 12px;">${t("admin.excusesManagement.rejected")}</div>
            </div>
          </div>
        </div>

        <!-- Table -->
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 12px;">
          <thead>
            <tr style="background-color: #4f46e5; color: white;">
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">${t("admin.excusesManagement.dateRange")}</th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">${t("admin.excusesManagement.student")}</th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">${t("admin.excusesManagement.room")}</th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">${t("admin.excusesManagement.reason")}</th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">${t("admin.excusesManagement.status")}</th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">${t("admin.excusesManagement.reviewedBy")}</th>
            </tr>
          </thead>
          <tbody>
            ${filteredExcuses.map((excuse, index) => {
              const getStatusColor = (status: string) => {
                switch (status) {
                  case "approved": return "#10b981";
                  case "rejected": return "#ef4444";
                  case "pending": return "#f59e0b";
                  default: return "#6b7280";
                }
              };

              const dateRange = excuse.startDate === excuse.endDate
                ? format(new Date(excuse.startDate), "dd/MM/yyyy", { locale })
                : `${format(new Date(excuse.startDate), "dd/MM/yyyy", { locale })} - ${format(new Date(excuse.endDate), "dd/MM/yyyy", { locale })}`;

              return `
                <tr style="background-color: ${index % 2 === 0 ? '#f9fafb' : 'white'};">
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-size: 11px;">${dateRange}</td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-weight: 500;">${excuse.studentName || t("admin.excusesManagement.unknownStudent")}</td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb;">${excuse.roomName || t("admin.excusesManagement.unknownRoom")}</td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-size: 11px;">${excuse.reason || t("admin.excusesManagement.noReasonProvided")}</td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb;">
                    <span style="color: ${getStatusColor(excuse.status)}; font-weight: bold;">
                      ${excuse.status ? t(`admin.excusesManagement.${excuse.status}`) : t("admin.excusesManagement.unknownStatus")}
                    </span>
                  </td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-size: 11px;">${excuse.reviewedBy || t("admin.excusesManagement.notReviewed")}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>

        <!-- Footer -->
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 11px;">
          <p style="margin: 0;">${t("admin.excusesManagement.generatedOn")} ${format(new Date(), "dd/MM/yyyy HH:mm", { locale })}</p>
          <p style="margin: 5px 0 0 0;">${currentBranding.APP_NAME} - ${t("admin.excusesManagement.excuseManagementSystem")}</p>
        </div>
      </div>
    `;
  };

  // Group excuses by status
  const pendingExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "pending"
  );
  const approvedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "approved"
  );
  const rejectedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "rejected"
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.excusesManagement.title")}</CardTitle>
        <CardDescription>
          {t("admin.excusesManagement.description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Statistics Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("admin.excusesManagement.total")}
                  </p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-muted-foreground opacity-50" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-yellow-600">
                    {t("admin.excusesManagement.pending")}
                  </p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500 opacity-50" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-green-600">
                    {t("admin.excusesManagement.approved")}
                  </p>
                  <p className="text-2xl font-bold">{stats.approved}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500 opacity-50" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-red-600">
                    {t("admin.excusesManagement.rejected")}
                  </p>
                  <p className="text-2xl font-bold">{stats.rejected}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-500 opacity-50" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex flex-col gap-4 mb-6">
          {/* Search Bar */}
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("admin.excusesManagement.searchPlaceholder")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 w-full"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 h-7 w-7"
                onClick={() => setSearchQuery("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Filters and Export - Responsive Layout */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
            {/* Teacher Filter */}
            <Select value={selectedTeacher} onValueChange={setSelectedTeacher}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue
                  placeholder={t("admin.excusesManagement.filterByTeacher")}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("admin.excusesManagement.allTeachers")}
                </SelectItem>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id}>
                    {teacher.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue
                  placeholder={t("admin.excusesManagement.filterByStatus")}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("admin.excusesManagement.allStatuses")}
                </SelectItem>
                <SelectItem value="pending">
                  {t("admin.excusesManagement.pending")}
                </SelectItem>
                <SelectItem value="approved">
                  {t("admin.excusesManagement.approved")}
                </SelectItem>
                <SelectItem value="rejected">
                  {t("admin.excusesManagement.rejected")}
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Export Button */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto flex items-center justify-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 border-blue-200 text-blue-700 hover:text-blue-800 transition-all duration-200 text-sm sm:text-base"
                >
                  <Download className="h-4 w-4 flex-shrink-0" />
                  <span className="hidden sm:inline">{t("admin.excusesManagement.export")}</span>
                  <span className="sm:hidden">Export</span>
                  <ChevronDown className="h-4 w-4 ml-1 flex-shrink-0" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={exportToHTML}
                  className="flex items-center gap-2 cursor-pointer hover:bg-blue-50 focus:bg-blue-50"
                >
                  <Globe className="h-4 w-4 text-blue-600" />
                  <span>{t("admin.excusesManagement.exportAsHTML")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToPDF}
                  className="flex items-center gap-2 cursor-pointer hover:bg-red-50 focus:bg-red-50"
                >
                  <FileImage className="h-4 w-4 text-red-600" />
                  <span>{t("admin.excusesManagement.exportAsPDF")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToCSV}
                  className="flex items-center gap-2 cursor-pointer hover:bg-green-50 focus:bg-green-50"
                >
                  <FileSpreadsheet className="h-4 w-4 text-green-600" />
                  <span>{t("admin.excusesManagement.exportAsCSV")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <Tabs defaultValue="management">
          <TabsList className="grid grid-cols-2 mb-4 h-auto">
            <TabsTrigger value="management" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
              <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="truncate">{t("admin.excusesManagement.management")}</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
              <Settings className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="truncate">{t("admin.excusesManagement.settings")}</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="management">
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid grid-cols-4 mb-4 h-auto">
                <TabsTrigger value="all" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
                  <span className="truncate">{t("admin.excusesManagement.all")}</span>
                  <Badge variant="secondary" className="text-xs px-1 py-0 min-w-0">
                    {filteredExcuses.length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="pending" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
                  <span className="truncate">{t("admin.excusesManagement.pending")}</span>
                  {pendingExcuses.length > 0 && (
                    <Badge variant="secondary" className="text-xs px-1 py-0 min-w-0">
                      {pendingExcuses.length}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="approved" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
                  <span className="truncate">{t("admin.excusesManagement.approved")}</span>
                  {approvedExcuses.length > 0 && (
                    <Badge variant="secondary" className="text-xs px-1 py-0 min-w-0">
                      {approvedExcuses.length}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="rejected" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
                  <span className="truncate">{t("admin.excusesManagement.rejected")}</span>
                  {rejectedExcuses.length > 0 && (
                    <Badge variant="secondary" className="text-xs px-1 py-0 min-w-0">
                      {rejectedExcuses.length}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                {loading ? (
                  <ExcusesSkeleton />
                ) : filteredExcuses.length === 0 ? (
                  <EmptyState
                    icon={
                      <FileText className="w-12 h-12 text-muted-foreground opacity-30" />
                    }
                    title={t("admin.excusesManagement.noExcusesFound")}
                    description={t(
                      "admin.excusesManagement.noExcuseRequestsMatching"
                    )}
                  />
                ) : (
                  <div className="space-y-4">
                    {filteredExcuses.map((excuse) => (
                      <ExcuseCard
                        key={excuse.id}
                        excuse={excuse}
                        onClick={() => {
                          setSelectedExcuse(excuse);
                          setTeacherNotes(excuse.notes || "");
                        }}
                        t={t}
                        getStatusBadge={getStatusBadge}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="pending">
                {loading ? (
                  <ExcusesSkeleton />
                ) : pendingExcuses.length === 0 ? (
                  <EmptyState
                    icon={
                      <Clock className="w-12 h-12 text-muted-foreground opacity-30" />
                    }
                    title={t("admin.excusesManagement.noPendingExcuses")}
                    description={t(
                      "admin.excusesManagement.noPendingExcuseRequests"
                    )}
                  />
                ) : (
                  <div className="space-y-4">
                    {pendingExcuses.map((excuse) => (
                      <ExcuseCard
                        key={excuse.id}
                        excuse={excuse}
                        onClick={() => {
                          setSelectedExcuse(excuse);
                          setTeacherNotes("");
                        }}
                        onDelete={() => setConfirmDelete(excuse.id)}
                        t={t}
                        getStatusBadge={getStatusBadge}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="approved">
                {loading ? (
                  <ExcusesSkeleton />
                ) : approvedExcuses.length === 0 ? (
                  <EmptyState
                    icon={
                      <CheckCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                    }
                    title={t("admin.excusesManagement.noApprovedExcuses")}
                    description={t(
                      "admin.excusesManagement.noApprovedExcuseRequests"
                    )}
                  />
                ) : (
                  <div className="space-y-4">
                    {approvedExcuses.map((excuse) => (
                      <ExcuseCard
                        key={excuse.id}
                        excuse={excuse}
                        onClick={() => {
                          setSelectedExcuse(excuse);
                          setTeacherNotes(excuse.notes || "");
                        }}
                        t={t}
                        getStatusBadge={getStatusBadge}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="rejected">
                {loading ? (
                  <ExcusesSkeleton />
                ) : rejectedExcuses.length === 0 ? (
                  <EmptyState
                    icon={
                      <XCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                    }
                    title={t("admin.excusesManagement.noRejectedExcuses")}
                    description={t(
                      "admin.excusesManagement.noRejectedExcuseRequests"
                    )}
                  />
                ) : (
                  <div className="space-y-4">
                    {rejectedExcuses.map((excuse) => (
                      <ExcuseCard
                        key={excuse.id}
                        excuse={excuse}
                        onClick={() => {
                          setSelectedExcuse(excuse);
                          setTeacherNotes(excuse.notes || "");
                        }}
                        t={t}
                        getStatusBadge={getStatusBadge}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </TabsContent>

          <TabsContent value="settings">
            <Tabs defaultValue="submission" className="w-full">
              <TabsList className="grid grid-cols-2 mb-4 h-auto">
                <TabsTrigger value="submission" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
                  <Settings className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="truncate">{t("admin.excusesManagement.submissionSettings")}</span>
                </TabsTrigger>
                <TabsTrigger value="cleanup" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-2 px-1 sm:px-3 text-xs sm:text-sm">
                  <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="truncate">{t("admin.excusesManagement.cleanupSettings")}</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="submission">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {t("admin.settings.excuseSubmissionSettings")}
                    </label>
                    <p className="text-sm text-muted-foreground">
                      {t("admin.settings.controlExcuseSubmission")}
                    </p>
                  </div>

                  <ExcuseSettings />

                  <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-md">
                    <p className="text-purple-800 text-sm">
                      {t("admin.settings.excuseTimeRestrictionInfo")}
                    </p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="cleanup">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {t("admin.excuseCleanup.title")}
                    </label>
                    <p className="text-sm text-muted-foreground">
                      {t("admin.excuseCleanup.description")}
                    </p>
                  </div>

                  <ExcuseCleanupSettings />
                </div>
              </TabsContent>
            </Tabs>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Excuse Details Dialog */}
      <Dialog
        open={!!selectedExcuse}
        onOpenChange={(open) => !open && setSelectedExcuse(null)}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {t("admin.excusesManagement.excuseRequestDetails")}
            </DialogTitle>
            <DialogDescription>
              {t("admin.excusesManagement.reviewStudentExcuse")}
            </DialogDescription>
          </DialogHeader>

          {selectedExcuse && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.student")}
                  </p>
                  <p className="font-medium">{selectedExcuse.studentName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.room")}
                  </p>
                  <p className="font-medium">
                    {t("admin.excusesManagement.room")}{" "}
                    {selectedExcuse.roomName}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.date")}
                  </p>
                  <p>
                    {format(
                      new Date(selectedExcuse.start_date),
                      "MMMM d, yyyy"
                    )}
                    {selectedExcuse.start_date !== selectedExcuse.end_date &&
                      ` - ${format(
                        new Date(selectedExcuse.end_date),
                        "MMMM d, yyyy"
                      )}`}
                  </p>
                  <p className="text-xs text-primary mt-1">
                    {t("admin.excusesManagement.duration")}:{" "}
                    {formatDuration(
                      calculateDaysBetween(
                        selectedExcuse.start_date,
                        selectedExcuse.end_date
                      )
                    )}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.time")}
                  </p>
                  <p>
                    {selectedExcuse.start_time} - {selectedExcuse.end_time}
                  </p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("admin.excusesManagement.reason")}
                </p>
                <div className="p-3 bg-muted rounded-md text-sm">
                  {selectedExcuse.reason}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("admin.excusesManagement.status")}
                </p>
                <div className="flex items-center">
                  {getStatusBadge(selectedExcuse.status)}
                  {selectedExcuse.status !== "pending" && (
                    <p className="text-sm ml-2">
                      {t("admin.excusesManagement.by")}{" "}
                      {selectedExcuse.teacherName ||
                        t("admin.excusesManagement.unknownTeacher")}
                    </p>
                  )}
                </div>
              </div>

              {selectedExcuse.status === "pending" ? (
                <>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("admin.excusesManagement.adminNotes")}
                    </p>
                    <Textarea
                      placeholder={t(
                        "admin.excusesManagement.addNotesPlaceholder"
                      )}
                      value={teacherNotes}
                      onChange={(e) => setTeacherNotes(e.target.value)}
                      className="resize-none"
                    />
                  </div>

                  <DialogFooter className="flex justify-between">
                    <Button
                      variant="destructive"
                      onClick={handleRejectExcuse}
                      disabled={isUpdating}
                      className="flex items-center gap-1"
                    >
                      {isUpdating ? (
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      ) : (
                        <X className="w-4 h-4" />
                      )}
                      {isUpdating ? t("common.processing") : t("admin.excusesManagement.reject")}
                    </Button>
                    <Button
                      onClick={handleApproveExcuse}
                      disabled={isUpdating}
                      className="flex items-center gap-1"
                    >
                      {isUpdating ? (
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      ) : (
                        <Check className="w-4 h-4" />
                      )}
                      {isUpdating ? t("common.processing") : t("admin.excusesManagement.approve")}
                    </Button>
                  </DialogFooter>
                </>
              ) : (
                selectedExcuse.notes && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("admin.excusesManagement.teacherNotes")}
                    </p>
                    <div className="p-3 bg-muted rounded-md text-sm">
                      {selectedExcuse.notes}
                    </div>
                  </div>
                )
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!confirmDelete}
        onOpenChange={(open) => !open && setConfirmDelete(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t("admin.excusesManagement.confirmDeletion")}
            </DialogTitle>
            <DialogDescription>
              {t("admin.excusesManagement.confirmDeleteExcuse")}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(null)}>
              {t("admin.excusesManagement.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={() => confirmDelete && handleDeleteExcuse(confirmDelete)}
            >
              {t("admin.excusesManagement.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Helper components
interface ExcuseCardProps {
  excuse: Excuse;
  onClick?: () => void;
  onDelete?: () => void;
  t: (key: string) => string;
  getStatusBadge: (status: string) => JSX.Element | null;
}

function ExcuseCard({ excuse, onClick, onDelete, t, getStatusBadge }: ExcuseCardProps) {
  const handleClick = (e: React.MouseEvent) => {
    // If we're clicking the delete button, don't trigger the card click
    if ((e.target as HTMLElement).closest(".delete-button")) {
      e.stopPropagation();
      return;
    }
    onClick?.();
  };

  return (
    <div
      className="border rounded-lg p-4 hover:border-primary/50 transition-colors cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-muted-foreground" />
            <h3 className="font-medium">
              {excuse.studentName ||
                t("admin.excusesManagement.unknownStudent")}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <DoorClosed className="w-4 h-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {t("admin.excusesManagement.room")}{" "}
              {excuse.roomName || t("admin.excusesManagement.unknownRoom")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(excuse.status)}
          {excuse.status === "pending" && onDelete && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-destructive delete-button"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1.5 text-muted-foreground">
            <Calendar className="w-3.5 h-3.5" />
            <span>
              {format(new Date(excuse.start_date), "MMM dd, yyyy")}
              {excuse.start_date !== excuse.end_date &&
                ` - ${format(new Date(excuse.end_date), "MMM dd, yyyy")}`}
            </span>
          </div>
          {excuse.start_date && excuse.end_date && (
            <div className="text-xs text-primary ml-5">
              {t("admin.excusesManagement.duration")}:{" "}
              {formatDuration(
                calculateDaysBetween(excuse.start_date, excuse.end_date)
              )}
            </div>
          )}
        </div>
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <Clock className="w-3.5 h-3.5" />
          <span>
            {excuse.start_time} - {excuse.end_time}
          </span>
        </div>
      </div>

      <div className="mt-2 flex items-start gap-1.5">
        <FileText className="w-3.5 h-3.5 text-muted-foreground mt-0.5" />
        <p className="text-sm text-muted-foreground line-clamp-1">
          {excuse.reason}
        </p>
      </div>
    </div>
  );
}

function createGetStatusBadge(t: (key: string) => string) {
  return function getStatusBadge(status: string) {
    switch (status) {
      case "pending":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"
          >
            <Clock className="w-3 h-3" />
            <span>{t("admin.excusesManagement.pending")}</span>
          </Badge>
        );
      case "approved":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
          >
            <CheckCircle className="w-3 h-3" />
            <span>{t("admin.excusesManagement.approved")}</span>
          </Badge>
        );
      case "rejected":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"
          >
            <XCircle className="w-3 h-3" />
            <span>{t("admin.excusesManagement.rejected")}</span>
          </Badge>
        );
      default:
        return null;
    }
  };
}

function EmptyState({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="text-center py-8">
      <div className="mx-auto mb-4">{icon}</div>
      <h3 className="text-lg font-medium mb-1">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
}

function ExcusesSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3].map((i) => (
        <div key={i} className="border rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <Skeleton className="h-5 w-[150px]" />
              <Skeleton className="h-4 w-[100px]" />
            </div>
            <Skeleton className="h-6 w-[80px]" />
          </div>
          <div className="mt-3 grid grid-cols-2 gap-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
          <div className="mt-2">
            <Skeleton className="h-4 w-full" />
          </div>
        </div>
      ))}
    </div>
  );
}
